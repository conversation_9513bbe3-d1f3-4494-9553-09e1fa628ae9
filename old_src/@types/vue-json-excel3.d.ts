declare module 'vue-json-excel3' {
  import { AllowedComponentProps, App, Component, ComponentCustomProps, VNodeProps } from 'vue'
  interface JsonExcelProps {
      data: string[][]; 
      escapeCsv: boolean;
      stringifyLongNum: boolean;
      name: string;
      type: string;

  }
  type JsonExcelType = JsonExcelProps & VNodeProps & AllowedComponentProps & ComponentCustomProps
  const JsonExcel: Component<JsonExcelType>
  export { JsonExcel }
  const def: { install: (app: App) => void }
  export default def
}