// dompurify.d.ts

declare module 'dompurify' {
  interface DOMPurifyConfig {
    ALLOWED_TAGS?: string[];
    ALLOWED_ATTR?: string[];
    [key: string]: any;
  }

  interface DOMPurify {
    sanitize(dirty: string | Node, config?: DOMPurifyConfig): string;
    addHook(entryPoint: string, hookFunction: (node: Element) => void): void;
    setConfig(config: DOMPurifyConfig): void;
  }

  const DOMPurify: DOMPurify;
  export default DOMPurify;
}
