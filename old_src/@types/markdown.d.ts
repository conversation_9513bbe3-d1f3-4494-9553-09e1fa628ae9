declare module "markdown-it" {
  // If you want to add specific type definitions for use, you can declare them here.
  // This is a simplified version; the full types for markdown-it are already included in the package.

  export interface MarkdownItOptions {
    html?: boolean;
    xhtmlOut?: boolean;
    breaks?: boolean;
    langPrefix?: string;
    linkify?: boolean;
    typographer?: boolean;
    quotes?: string;
  }

  export default class MarkdownIt {
    constructor(options?: MarkdownItOptions);
    render(md: string): string;
    // Define other methods if you use them
  }
}
