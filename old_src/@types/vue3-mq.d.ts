declare module 'vue3-mq' {
  import { Component, Plugin } from 'vue'

  export const Vue3Mq: Plugin<{ preset: string }>

  export const useMq: () => {
    current: string
    xs: boolean
    smMinus: boolean
    smPlus: boolean
    sm: boolean
    mdMinus: boolean
    mdPlus: boolean
    md: boolean
    lgMinus: boolean
    lgPlus: boolean
    lg: boolean
    xlMinus: boolean
    xlPlus: boolean
    xl: boolean
    xxl: boolean
    orientation: 'landscape' | 'portrait'
    isLandscape: boolean
    isPortrait: boolean
    theme: 'light' | 'dark'
    isDark: boolean
    isLight: boolean
    motionPreference: 'reduce' | 'no-preference'
    isMotion: boolean
    isInert: boolean
  }

  export const MqResponsive: Component
}
