<script setup lang="ts">
import { getStatisticCounters, getStatisticByPath, getStatisticByDate } from '@lib/api'
import dayjs from 'dayjs'
import { ref } from 'vue'

interface IDataCard {
  total_request: number
  Success: number
  Fail: number
  Error: number
}

interface IDataChart {
  label: string
  data: any[]  // eslint-disable-line @typescript-eslint/no-explicit-any
  fill: boolean
  borderColor: string
  tension: number
}

const dateRange = ref([
  dayjs().startOf('month').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
])

const fields = ref([
  { key: 'api', label: 'APIs', align: 'left' },
  { key: 'all_request', label: 'All Requests' },
  { key: 'success', label: 'Success' },
  { key: 'fail', label: 'Fail' },
  { key: 'error', label: 'Error', align: 'left' }
])
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const tableDataAccordingAPI = ref<{[key: string]: any}[]>()
const dataCard = ref<IDataCard>({
  total_request: 0,
  Success: 0,
  Fail: 0,
  Error: 0
})
const loading = ref(false)
const chartLabel = ref<string[]>()
const showChart = ref(false)
const dataChart = ref<IDataChart[]>()

const fetchData = async () => {
  loading.value = true
  await Promise.all([
    await getRequestStatus(),
    await getStatisticsAccordingAPI(),
    await getDataChart()
  ])
  loading.value = false
}

const getRequestStatus = async () => {
  await getStatisticCounters(
    dateRange.value[0] + ' 00:00:00',
    dateRange.value[1] + ' 23:59:59'
  ).then(({ data }) => {
    dataCard.value = data.data
    dataCard.value.total_request =
      dataCard.value.Success + dataCard.value.Fail + dataCard.value.Error
  })
}

const getStatisticsAccordingAPI = async () => {
  await getStatisticByPath(dateRange.value[0] + ' 00:00:00', dateRange.value[1] + ' 23:59:59').then(
    ({ data }) => {
      let dataFormat: {[key: string]: string|number}[] = []
      let keys = Object.keys(data.data)
      keys.forEach((key) => {
        dataFormat.push({
          api: key,
          all_request: data.data[key].Total,
          success: data.data[key].Success,
          fail: data.data[key].Fail,
          error: data.data[key].Error
        })
      })
      tableDataAccordingAPI.value = dataFormat
    }
  )
}

const getDataChart = async () => {
  await getStatisticByDate(dateRange.value[0] + ' 00:00:00', dateRange.value[1] + ' 23:59:59').then(
    ({ data }) => {
      formatDataChart(data.data)
    }
  )
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const formatDataChart = (data: {[key: string]: any}) => {
  chartLabel.value = getDatesInRange(
    new Date(dateRange.value[0]),
    new Date(dateRange.value[1])
  )
  dataChart.value = [
    {
      label: 'All Request',
      data: [],
      fill: false,
      borderColor: 'rgb(80 183 236)',
      tension: 0.1
    },
    {
      label: 'Success',
      data: [],
      fill: false,
      borderColor: 'rgb(80, 135, 236)',
      tension: 0.1
    },
    {
      label: 'Fail',
      data: [],
      fill: false,
      borderColor: 'rgb(236 137 80)',
      tension: 0.1
    },
    {
      label: 'Error',
      data: [],
      fill: false,
      borderColor: 'rgb(236 80 80)',
      tension: 0.1
    }
  ]
  chartLabel.value.forEach((label) => {
    let key = dayjs(label).format('DD/MM/YYYY')
    if (dataChart.value) {
      // eslint-disable-next-line no-prototype-builtins
      if (data.hasOwnProperty(key)) {
        dataChart.value[0].data.push(data[key].Total)
        dataChart.value[1].data.push(data[key].Success)
        dataChart.value[2].data.push(data[key].Fail)
        dataChart.value[3].data.push(data[key].Error)
      } else {
        dataChart.value[0].data.push(0)
        dataChart.value[1].data.push(0)
        dataChart.value[2].data.push(0)
        dataChart.value[3].data.push(0)
      }
    }
  })
  showChart.value = true
}

const getDatesInRange = (startDate: Date, endDate: Date) => {
  const date = new Date(startDate.getTime())
  const dates = []

  while (date <= endDate) {
    dates.push(dayjs(date).format('YYYY/MM/DD'))
    date.setDate(date.getDate() + 1)
  }
  return dates
}

fetchData()

</script>

<template>
  <div v-loading.fullscreen="loading" class="page-analysis">
    <div class="grid grid-cols-21 bg-white">
      <div class="main-content col-span-20 col-start-2">
        <div class="flex">
          <i class="ri-bar-chart-grouped-fill text-[30px] mr-[15px]"></i>
          <h2 class="text-[30px]">Statistic</h2>
        </div>

        <div class="mt-[30px]">
          <span class="font-bold mr-[40px]">Time range:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="Start date"
            end-placeholder="End date"
            :clearable="false"
            size="large"
            class="!h-[38px]"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            @change="fetchData"
          />
          <div class="mt-[25px] flex justify-between">
            <div class="card all-request">
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-bold text-[#333] text-[18px] mb-[10px]">Total Request</div>
                  <span class="card-number text-[28px] font-bold">{{ dataCard.total_request }}
                    <span class="card-unit text-[14px]">Turns</span>
                  </span>
                </div>
                <div>
                  <i class="ri-information-2-line text-[30px]"></i>
                </div>
              </div>
            </div>
            <div class="card success">
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-bold text-[#333] text-[18px] mb-[10px]">Success</div>
                  <span class="card-number text-[28px] font-bold">{{ dataCard.Success }}
                    <span class="card-unit text-[14px]">Turns</span>
                  </span>
                </div>
                <div>
                  <i class="ri-checkbox-circle-fill text-[30px]"></i>
                </div>
              </div>
            </div>
            <div class="card client-error">
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-bold text-[#333] text-[18px] mb-[10px]">Fail</div>
                  <span class="card-number text-[28px] font-bold">{{ dataCard.Fail }}
                    <span class="card-unit text-[14px]">Turns</span>
                  </span>
                </div>
                <div>
                  <i class="ri-alert-line text-[30px]"></i>
                </div>
              </div>
            </div>
            <div class="card system-error">
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-bold text-[#333] text-[18px] mb-[10px]">Error</div>
                  <span class="card-number text-[28px] font-bold">{{ dataCard.Error }}
                    <span class="card-unit text-[14px]">Turns</span>
                  </span>
                </div>
                <div>
                  <i class="ri-close-circle-fill text-[30px]"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="showChart" class="py-[20px] px-[30px] mt-[30px] chart">
          <BarChart v-if="showChart" :labels="chartLabel" :data="dataChart" />
        </div>

        <div class="py-[20px] px-[30px] mt-[30px] chart">
          <DataTable :fields="fields" :items="tableDataAccordingAPI" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main-content {
  min-height: 100vh;
  background: #fff;
  padding: 20px;
}
.card {
  box-shadow: 0px 0px 12px rgb(0 0 0 / 12%);
  border-radius: 10px;
  padding: 25px;
  flex-basis: 23%;
}
.card.success {
  border-left: 3px solid rgb(80, 135, 236);
  color: rgb(80, 135, 236);
}
.card.all-request {
  border-left: 3px solid rgb(80 183 236);
  color: rgb(80 183 236);
}
.card.client-error {
  border-left: 3px solid rgb(236 137 80);
  color: rgb(236 137 80);
}
.card.system-error {
  border-left: 3px solid rgb(236 80 80);
  color: rgb(236 80 80);
}
.chart {
  box-shadow: 0px 0px 12px rgb(0 0 0 / 12%);
}
</style>
