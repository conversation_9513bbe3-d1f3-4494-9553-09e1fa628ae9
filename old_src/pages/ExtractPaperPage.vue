<script setup lang="ts">
import DataTable from '@components/DataTable.vue'
import ImageCarousel from '@components/ImageCarousel.vue'
import ImgViewer from '@components/ImgViewer.vue'
import JsonViewer from '@components/JsonViewer.vue'
import PdfViewerAfterUpload from '@components/PdfViewerAfterUpload.vue'
import { getExtractedInfo } from '@lib/api'
import {
  getImageList,
  IImageList,
  PAPER_PROBLEMS,
  resetSelectProblems,
  SUB_PROBLEMS_CARD_EXTRACT,
  SUB_PROBLEMS_ID_CARD_EN,
  SUB_PROBLEMS_ID_CARD_FR,
  SUB_PROBLEMS_ID_CARD_JP,
  SUB_PROBLEMS_ID_CARD_VN,
  SUB_PROBLEMS_INVOICE_EXTRACT,
  SUB_PROBLEMS_RECEIPT_EXTRACT,
  TABLE_LANGUAGE,
  VIETNAM_CERTIFICATE_CONVERT_TO_FRENCH
} from '@store/const'
import { AxiosError, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'
import JsonExcel from 'vue-json-excel3'

interface ICustomEvent extends Event {
  files: FileList
  action: string
}

interface INewFile extends File {
  typeFile: string
  url: string
}

interface IExtractedInfo {
  type: string
  text: string
  confidence: number
}

interface IDataResult {
  entity_type: string
  entity_text: string
  confidence: number
}

interface IRawJson {
  [key: string]: string
}


const fields = ref([
  { key: 'entity_type', label: 'Entity Type' },
  { key: 'entity_text', label: 'Entity Text' },
  { key: 'confidence', label: 'Confidence' }
])
const url = ref<string|null>(null)
const fileUpload = ref<File|INewFile|null>(null)
const viewResult = ref('table')
const dataViewJson = ref<IRawJson>({})
const dataResults = ref<IDataResult[]>([])
const showBtnSubmit = ref(false)
const uploaded = ref(false)
const submitted = ref(false)
const totalTimeRequest = ref<string>()
const uploadFile = ref<INewFile|null>()
const currentFile = ref(-1)
const fileLoading = ref<boolean>()
const timeRequests = ref<string>('')
const convertKeyToFrench = ref(VIETNAM_CERTIFICATE_CONVERT_TO_FRENCH)
const problem = ref<string>('')
const problems = ref(PAPER_PROBLEMS)
const language = ref<string>('')
const languages = ref(TABLE_LANGUAGE)
const selectedSubProblemId = ref<string>('')
const setImageList = ref<IImageList[]>()
const width1 = ref<number>(900)
const ocrText = ref<string>('')

const block1 = ref<HTMLDivElement|null>(null)
const file = ref<HTMLInputElement|null>(null)

const drag = (e: MouseEvent) => {
  let dragX = e.clientX
  let block = block1.value
  document.onmousemove = function onMouseMove(e) {
    if (block) {
      width1.value = block.offsetWidth + e.clientX - dragX
      block.style.width = width1.value + 'px'
    }
    dragX = e.clientX
  }
  // remove mouse-move listener on mouse-up
  document.onmouseup = () => (document.onmousemove = document.onmouseup = null)
}

// eslint-disable-next-line
const handleDragOver = (event: any) => {
  event.currentTarget.classList.add("dragging");
};

// eslint-disable-next-line
const handleDragLeave = (event: any) => {
  event.currentTarget.classList.remove("dragging");
};

// eslint-disable-next-line
const handleDrop = (event: any) => {
  event.preventDefault();
  event.stopPropagation();
  event.currentTarget.classList.remove("dragging");
  fileUpload.value = event.dataTransfer.files[0]
  handleUploadFile(event)
}

const handleUploadFile = (event: Event) => {
  if (!selectedSubProblemId.value) {
    ElMessage({ message: 'Please select type', type: 'error', grouping: true })
  } else {
    let maxFileSize = 10 * 1024 * 1024 // Max file size 10MB
    let minFileSize = 100 * 1024 // Min file size 100KB
    if (event.target instanceof HTMLInputElement && event.target.files && event.target.value) {
      if (!event.target.files[0]) {
        return
      }

      fileUpload.value = event.target.files[0]
    }
    if (fileUpload.value && fileUpload.value.size > maxFileSize) {
      ElMessage({ message: 'File size must not exceed 10MB', type: 'error' })
      fileUpload.value = null
      return
    }
    if (fileUpload.value && fileUpload.value.size < minFileSize) {
      ElMessage({ message: 'File size must be at least 100KB', type: 'error' })
      fileUpload.value = null
      return
    }
    if (fileUpload.value && fileUpload.value.type.startsWith("image/")) {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const img = new Image();

        // Once the image is loaded, check its dimensions
        img.onload = () => {
          const width = img.width;
          const height = img.height;

          // Validate the dimensions
          if (width < 500 || height < 400) {
            ElMessage({ message: `Image is too small. Minimum size is 500x400 pixels. Your image is ${width}x${height} pixels.`, type: 'error' })
            fileUpload.value = null
            return
          }
          if (fileUpload.value) {
            modUploadFile(fileUpload.value); // Only call this if validation passes
          }
        }
        img.onerror = () => {
          ElMessage({ message: 'Invalid image file.', type: 'error' });
          fileUpload.value = null;
          return
        };

        // Set the image source to the FileReader result
        img.src = e.target?.result as string;
      };

      // Read the file as a data URL
      reader.readAsDataURL(fileUpload.value);
    }
    else if (fileUpload.value && fileUpload.value.type.includes("pdf")) modUploadFile(fileUpload.value)
    else ElMessage({ message: 'Please upload image or pdf', type: 'error', grouping: true })
  }
}

const modUploadFile = async (f: File) => {
  url.value = URL.createObjectURL(f)

  showBtnSubmit.value = true
  let newFile: File = f
  uploadFile.value = {
    ...newFile,
    name: newFile.name,
    size: newFile.size,
    typeFile: newFile.type.includes('/pdf') ? 'pdf' : 'image',
    url: URL.createObjectURL(newFile),
  }
  if (submitted.value && !fileLoading.value) fileLoading.value = true
  uploaded.value = true
  if (file.value) file.value.value = ''
}

const handleChooseFile = (event: ICustomEvent) => {
  if (!selectedSubProblemId.value) {
    ElMessage({ message: 'Please select type', type: 'error', grouping: true })
  } else {
  if (!event.action) {
    return
  }

  resetData()
  fileUpload.value = event.files[0]
  modUploadFile(fileUpload.value)

  }
}

const resetData = () => {
  url.value = null
  fileUpload.value = null
  uploadFile.value = null
  showBtnSubmit.value = false
  uploaded.value = false
  submitted.value = false
  totalTimeRequest.value = ''
  dataResults.value = []
  ocrText.value = ''
  timeRequests.value = ''
  fileLoading.value = false
  dataViewJson.value = {}
  currentFile.value = -1
}

const submit = async () => {
  submitted.value = true
  fileLoading.value = true
  showBtnSubmit.value = false
  if (!fileUpload.value) {
    ElMessage({ message: 'Please reupload image', type: 'error', grouping: true })
    return
  }
  await getExtractedInfo(
    fileUpload.value,
    selectedSubProblemId.value,
    language.value
  )
    .then((data) => {
      catchSuccessClassify(data)
    })
    .catch((error) => {
      catchErrorClassify(error)
    })
}

const catchSuccessClassify = (data: AxiosResponse) => {
  const extracted_info: IExtractedInfo[] = data.data.data.extracted_info
  ocrText.value = data.data.data.text
  let formatData: IDataResult[] = []
  let jsonData: IRawJson = {}

  extracted_info.forEach((item: IExtractedInfo) => {
    const entityTextCard = item.text == 'ID Card' ? 'Oui' : 'Non'
    formatData.push({
      entity_type: convertKeyToFrench.value[`${item.type}`]
        ? convertKeyToFrench.value[`${item.type}`]
        : item.type,
      entity_text: item.type == 'docType' 
        ? entityTextCard 
        : item.text,
      confidence: item.confidence
    })
    jsonData[item.type] = item.text
  })

  dataResults.value = formatData
  dataViewJson.value = jsonData
  fileLoading.value = false
  submitted.value = false
  showBtnSubmit.value = true
  timeRequests.value = (data.headers['x-process-time'] * 1000).toFixed()
}

const catchErrorClassify = (error: AxiosError) => {
  ElMessage({
    // @ts-ignore
    message: error?.response?.data?.message ?? 'Something went wrong',
    type: 'error',
    grouping: true
  })
  fileLoading.value = false
  submitted.value = false
  showBtnSubmit.value = true
}

const formatDataExport = () => {
  return [dataViewJson.value]
}


const subProblems = computed(() => {
  if (problem.value == 'ID_CARD') {
    if (language.value == 'vi') return SUB_PROBLEMS_ID_CARD_VN
    else if (language.value == 'en') return SUB_PROBLEMS_ID_CARD_EN
    else if (language.value == 'fr') return SUB_PROBLEMS_ID_CARD_FR
    else if (language.value == 'ja') return SUB_PROBLEMS_ID_CARD_JP
    else return [{ id: 1, name: 'Unknown', label: 'Unknown', active: false }]
  }
  else if (problem.value == 'INVOICE') return SUB_PROBLEMS_INVOICE_EXTRACT
  else if (problem.value == 'RECEIPT') return SUB_PROBLEMS_RECEIPT_EXTRACT
  else if (problem.value == 'CARD') return SUB_PROBLEMS_CARD_EXTRACT
  else return [{ id: 1, name: 'Unknown', label: 'Unknown', active: false }]
})

const selectSubProblem = (subProblemId: string) => {
  selectedSubProblemId.value = subProblemId
  problems.value.map((item) => {
    if (item.name == subProblemId) {
      item.active = true
    } else {
      item.active = false
    }
  })
  setImageList.value = getImageList(language.value, subProblemId)
  resetData()
}

const resetProblem = () => {
  problem.value = ''
  selectedSubProblemId.value = ''
  url.value = null
  fileUpload.value = null
  uploadFile.value = null
  uploaded.value = false
}

const resetSubProblem = () => {
  if (subProblems.value.length == 1) {
    selectedSubProblemId.value = subProblems.value[0].name
    selectSubProblem(selectedSubProblemId.value)
  }
  else selectedSubProblemId.value = ''
  url.value = null
  fileUpload.value = null
  uploadFile.value = null
  uploaded.value = false
}

const handleFileClick = () => {
  if (!selectedSubProblemId.value) {
    ElMessage({
      message: 'Please select paper type',
      type: 'error',
      grouping: true
    })
  } else file.value?.click()
}

resetSelectProblems(problems.value)

</script>

<template>
  <div id="app" style="margin: 0 20px !important">
    <div class="mt-[30px] mb-[30px] grid grid-cols-21 gap-2">
      <el-select
        v-model="language"
        placeholder="Select language"
        size="large"
        class="col-start-2 col-span-4"
        @change="resetProblem"
      >
        <el-option
          v-for="item in languages"
          :key="item.id"
          :label="item.label"
          :value="item.name"
        />        
      </el-select>
      <el-select
        v-model="problem"
        placeholder="Select paper type"
        size="large"
        class="col-span-4"
        :disabled="language ? false : true"
        @change="resetSubProblem"
      >
        <el-option
          v-for="item in problems"
          :key="item.id"
          :label="item.label"
          :value="item.name"
        />        
      </el-select>
      <el-select
        v-model="selectedSubProblemId"
        placeholder="Select sub type"
        size="large"
        class="col-span-4"
        :disabled="language && problem ? false : true"
        @change="selectSubProblem"
      >
        <el-option
          v-for="item in subProblems"
          :key="item.id"
          :label="item.label"
          :value="item.name"
        />        
      </el-select>
      <div v-if="uploaded" class="col-span-4 col-start-18 grid grid-cols-4 gap-2">
        <el-button
          type="danger"
          class="btn-upload col-span-2"
          plain
          @click="resetData"
        >
          <i class="ri-close-line mr-1 relative top-[-1px] text-[18px]"></i>
          Reset
        </el-button>

        <el-button
          v-if="showBtnSubmit"
          color="#0082BE"
          type="primary"
          class="btn-upload col-span-2"
          @click="submit"
        >
          <i class="ri-corner-up-right-fill relative top-[-1px] text-[18px] mr-1"></i>
          Executer
        </el-button>
      </div>
    </div>

    <div class="mt-[10px] grid grid-cols-21">
      <div class="col-span-20 col-start-2 flex" :class="selectedSubProblemId ? 'h-[800px]' : 'h-[600px]'">
        <div ref="block1" class="block block-1 w-[60%] min-w-[300px] h-[600px]">
          <div>
            <input
              v-if="selectedSubProblemId"
              ref="file"
              accept=".jpg,.png,.bmp,.jpeg,.tif,.gif,.pdf"
              type="file"
              hidden
              @change="handleUploadFile"
            />
          </div>
          <div v-if="url && uploadFile" class="h-[600px] w-full flex justify-center bg-[#ccc]">
            <div v-if="uploadFile.typeFile !== 'pdf'" class="overflow-auto w-full">
              <ImgViewer :url="uploadFile.url" />
            </div>
            <div v-else class="h-full w-full">
              <PdfViewerAfterUpload :width="width1" :url="uploadFile.url" />
            </div>
          </div>
          <div 
            v-if="!uploaded"
            class="h-[600px] p-[20px]"
            @click="handleFileClick"
            @dragover.prevent="handleDragOver"
            @dragleave="handleDragLeave"
            @drop.prevent="handleDrop"
          >
            <div class="h-full flex items-center justify-center flex-col wrap-upload">
              <i class="ri-contacts-book-upload-line text-[34px]"></i>
              <div class="text-[20px]">Add files</div>
            </div>
          </div>
          <ImageCarousel
            v-if="selectedSubProblemId"
            class="h-[200px]"
            :images-list="setImageList"
            :width="width1"
            @upload-this-file="handleChooseFile"
          />
        </div>

        <div class="block min-w-[300px] overflow-hidden flex-1 h-[100%]">
          <div class="flex h-[100%]">
            <div class="slider" @mousedown="drag"></div>
            <div v-loading="fileLoading" class="block block-2">
              <div class="px-[15px] pt-[20px] grid grid-cols-8">
                <div v-if="uploadFile == null" class="font-[600] text-[20px]">Result</div>
                <div v-else class="font-[600] text-[20px] col-span-5 text-[#333333e0] file-name-result">
                  {{ uploadFile.name }}
                </div>
                <JsonExcel
                  v-if="dataResults.length"
                  class="col-start-7 col-span-2"
                  :data="formatDataExport()"
                  type="csv"
                  name="results.csv"
                >
                  <el-button
                    class="ml-[13px]"
                    color="#0082BE"
                  >
                    <i class="ri-download-2-line mr-1 relative top-[-1px] text-[18px]"></i>
                    Exporter
                  </el-button>
                </JsonExcel>
              </div>
              <div class="grid grid-cols-2 mt-[20px]" style="background: #f3f3f3">
                <div
                  class="px-[15px] pt-[10px] pb-[10px] cursor-pointer text-[#747474] flex justify-center items-center"
                  :class="{ 'result-tab-active': viewResult == 'table' }"
                  @click="viewResult = 'table'"
                >
                  <p>Result format</p>
                </div>
                <div
                  class="px-[15px] pt-[10px] pb-[10px] cursor-pointer text-[#747474] flex justify-center items-center"
                  :class="{ 'result-tab-active': viewResult == 'json' }"
                  @click="viewResult = 'json'"
                >
                  <p>Raw Json</p>
                </div>
                <!-- <div
                  class="px-[15px] pt-[10px] pb-[10px] cursor-pointer text-[#747474] flex justify-center items-center"
                  :class="{ 'result-tab-active': viewResult == 'text' }"
                  @click="viewResult = 'text'"
                >
                  <p>Raw Text</p>
                </div> -->
              </div>
              <div id="table-result-upload" class="h-[80%]">
                <DataTable
                  v-if="viewResult == 'table'"
                  id="table-result-scroll"
                  :fields="fields"
                  :items="dataResults"
                />
                <JsonViewer
                  v-if="viewResult == 'json' && dataViewJson"
                  :data-view-json="dataViewJson"
                />
                <div v-if="viewResult == 'text' && ocrText" class="overflow-auto h-[95%]">
                  <p class="text-[16px] p-5 whitespace-pre-line">{{ ocrText }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.file-name {
  word-break: break-all;
  white-space: unset;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.slider {
  line-height: 100%;
  width: 10px;
  background-color: #ffffffb8;
  border: none;
  cursor: col-resize;
  user-select: none;
  text-align: center;
  height: 100%;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

.result-tab-active {
  border-bottom: 3px solid #333;
  color: #000000 !important;
  font-weight: 500;
}

.btn-upload {
  border-radius: 5px;
  padding: 20px;
  font-size: 16px;
}

.wrap-upload {
  border: 2px dotted #acaaaa;
  border-radius: 10px;
  color: #787878;
  cursor: pointer;
  height: 100%;
}

.wrap-upload:hover {
  color: #0082be;
  border-color: #0082be;
}

.dragging {
  background-color: #f0f8ff;
}

.el-button + .el-button {
  margin-left: 0px !important;
}

</style>
