<script setup lang="ts">
import DataTable from '@components/DataTable.vue'
import { getAllRequest, getFileWithID, getPostCheckById } from '@lib/api'
import moment from 'moment'
import mime from 'mime'
import { ref, watch } from 'vue'
import { ElMessage, type ElDropdown } from 'element-plus'

defineEmits(['closeValidationForm'])
const dateRange = ref([
  moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
  moment().format('YYYY-MM-DD HH:mm:ss')
])

interface IExtractedInfo {
  type: string
  text: string
}

interface IField {
  key: string
  label: string
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  width?: string | number
  minWidth?: string | number
  type?: 'default' | 'selection' | 'index' | 'expand'
  sortable?: boolean | string
}

interface IDetailRequest {
  id: string
  code: string
  created_at: string
  status: string
  api_path: string
  file_uris: string[]
}

const paginate = ref({
  per_page: 10,
  total: 10,
  current_page: 1,
  last_page: 1
})

const filter = ref({
  page: 1,
  limit: 10
})
const loading = ref(false)
const search = ref('')
const fields = ref<IField[]>([
  { key: 'id', label: 'ID', align: 'left' },
  { key: 'code', label: 'Code' },
  { key: 'api_path', label: 'Api Path' },
  { key: 'created_at', label: 'Created at' },
  { key: 'status', label: 'Status', align: 'center' }
])

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const tableDataAccordingAPI = ref<any[]>()
const status = ref('')
const statusOptions = ref([
  { id: 1, label: 'All request', value: '' },
  { id: 2, label: 'Success', value: 'Success' },
  { id: 3, label: 'Fail', value: 'Fail' },
  { id: 3, label: 'Error', value: 'Error' }
])

const openUpdatePostCheck = ref(false)
const countFilterStatus = ref(0)
const activeName = ref('validation')
const detailRequestCustom = ref<IDetailRequest|any>({})  // eslint-disable-line @typescript-eslint/no-explicit-any
const dataResults = ref<IExtractedInfo[]>([])  // eslint-disable-line @typescript-eslint/no-explicit-any
const url = ref<string>('')
const contentType = ref<'pdf'|'image'|null>(null)
const ocrText = ref<string>('')

const filterStatus = ref<InstanceType<typeof ElDropdown> | null>(null)

watch(status, () => {
  fetchData()
})

watch(search, () => {
  fetchData()
})

watch(dateRange, () => {
  fetchData()
})

const fetchData = async () => {
  await getAllRequest(
    dateRange.value[0],
    dateRange.value[1],
    search.value,
    status.value,
    filter.value.page,
    filter.value.limit
  ).then((res) => {
    if (res?.status == 200) {
      tableDataAccordingAPI.value = res?.data?.data?.data
      paginate.value.per_page = filter.value.limit
      paginate.value.total = res?.data?.data?.total_count
      paginate.value.current_page = filter.value.page
      paginate.value.last_page = res?.data?.data?.total_pages
    }
  })
}

const changePerPage = (val: number) => {
  filter.value.page = 1
  filter.value.limit = val
  fetchData()
}

const handleCurrentPage = (val: number) => {
  filter.value.page = val
  fetchData()
}

const changeFilterStatus = () => {
  if (status.value != '') {
    countFilterStatus.value = 1
  }
  filterStatus.value?.handleOpen()
  fetchData()
}

const resetFilterStatus = () => {
  status.value = ''
  countFilterStatus.value = 0
  fetchData()
}

const beforeClose = () => {
  const updatePostCheck = document.getElementsByClassName('update-post-check')
  updatePostCheck[0].classList.add('closing')
  setTimeout(() => {

  }, 300)
  openUpdatePostCheck.value = false

}

const openScreenUpdatePost = async (id: string) => {
  await getPostCheckById(id).then((res) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const detailRequest= tableDataAccordingAPI.value?.filter((item: any) => item.id == id)
    if (detailRequest && detailRequest.length > 0) {
      detailRequestCustom.value = detailRequest[0]
      const file_uris = detailRequestCustom.value?.file_uris ?? []
      if (file_uris.length > 0) {
        const ext = file_uris[0].split('.')[file_uris[0].split('.').length - 1]
        const type = mime.getType(ext)
        if (type =='application/pdf') {
          contentType.value = 'pdf'
        } else if (type?.startsWith('image')) {
          contentType.value = 'image'
        } else {
          contentType.value = null
          ElMessage({ message: 'File type not supported', type: 'error', grouping: true })
        }
      }

    }
    dataResults.value = res.data.data.data.result.data[0].extracted_info || []
    ocrText.value = res.data.data.data.result.data[0].text
    openUpdatePostCheck.value = true
  })
  await getFileAndDisplay(id)
}

const getFileAndDisplay = async (id: string) => {
  const res = await getFileWithID(id)
  if (res?.status == 200) {
    const blob = await res.data
    url.value = URL.createObjectURL(blob)
  }
  else {
    url.value = ''
    contentType.value = null
    ElMessage({ message: res?.data?.message, type: 'error', grouping: true })
  }
}


fetchData()

</script>

<template>
  <div v-loading.fullscreen="loading" class="page-analysis">
    <div class="grid grid-cols-21 bg-white">
      <div class="main-content col-start-2 col-span-20">
        <div class="flex">
          <i class="ri-bar-chart-grouped-fill text-[30px] mr-[15px]"></i>
          <h2 class="text-[30px]">Post check</h2>
        </div>

        <div class="mt-[30px]">
          <span class="font-bold mr-[40px]">Time range:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="Start date"
            end-placeholder="End date"
            :clearable="false"
            size="large"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="!h-[38px]"
          />
        </div>
        <div class="mt-[30px]">
          <el-card class="w-full">
            <div class="flex justify-end items-center">
              <el-input
                v-model="search"
                style="width: 300px"
                size="large"
                placeholder="Search by PATH"
              >
                <template #prefix>
                  <i class="ri-search-line el-input__icon"></i>
                </template>
              </el-input>
              <el-dropdown ref="filterStatus" class="ml-[20px]" trigger="click">
                <span class="el-dropdown-link cursor-pointer relative">
                  <i class="ri-filter-line !text-2xl"></i>
                  <div
                    class="absolute w-[20px] h-[20px] rounded !bg-[#fdf6ec] text-center top-[-11px] right-[-9px] border flex items-center justify-between"
                  >
                    <span class="text-[#e7a76a] inline-block w-full">{{ countFilterStatus }}</span>
                  </div>
                </span>
                <template #dropdown>
                  <el-dropdown-menu class="!w-full el-filter-status">
                    <div class="flex items-center justify-between !w-full px-[15px] py-[10px]">
                      <div class="font-semibold text-base capitalize">filter</div>
                      <div
                        class="!text-[red] font-semibold text-base cursor-pointer"
                        @click="resetFilterStatus"
                      >
                        Clear
                      </div>
                    </div>
                    <div class="px-[15px]">
                      <p class="font-normal text-sm !text-[#808185] pb-[10px]">Status</p>
                      <el-select
                        v-model="status"
                        style="width: 250px"
                        size="large"
                        class="mb-[10px]"
                        @change="changeFilterStatus"
                      >
                        <el-option
                          v-for="item in statusOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </div>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="mt-[10px]">
              <DataTable :fields="fields" :items="tableDataAccordingAPI">
                <template #status="{ row }">
                  <el-check-tag
                    v-if="row?.status == 'Success'"
                    class="!text-[#72c749] !bg-[#f0f9eb]"
                  >
                    {{ row?.status }}
                  </el-check-tag>
                  <el-check-tag v-else class="!text-[#f67d7d] !bg-[#fef0f0]">
                    {{
                      row?.status
                    }}
                  </el-check-tag>
                </template>
                <template #id="{ row }">
                  <span
                    class="cursor-pointer post-id"
                    @click.prevent="openScreenUpdatePost(row?.id)"
                  >{{ row?.id }}</span>
                </template>
              </DataTable>
              <div
                v-if="tableDataAccordingAPI && tableDataAccordingAPI?.length > 0"
                class="table-pagination flex justify-end items-end mt-[30px] relative"
              >
                <el-pagination
                  :current-page="Number(paginate.current_page) || 1"
                  :page-size="paginate.per_page"
                  :small="false"
                  :disabled="false"
                  :background="false"
                  layout="sizes, prev, pager, next"
                  :total="paginate.total"
                  @current-change="handleCurrentPage"
                  @size-change="changePerPage"
                />
              </div>
            </div>
          </el-card>
        </div>

        <el-dialog
          v-model="openUpdatePostCheck"
          title="Validation details"
          width="50%"
          top="0"
          class="update-post-check custom-dialog"
          destroy-on-close
          :before-close="beforeClose"
        >
          <el-tabs v-model="activeName" class="post-check-tabs !text-base">
            <el-tab-pane label="Validation" name="validation" class="!text-base">
              <div class="grid grid-cols-3 w-full">
                <p class="col-span-1 mt-2">ID:</p>
                <p class="col-span-2 mt-2">{{ detailRequestCustom.id }}</p>
                
                <p class="col-span-1 mt-2">Code:</p>
                <p class="col-span-2 mt-2">{{ detailRequestCustom.code }}</p>
                
                <p class="col-span-1 mt-2">Created at:</p>
                <p class="col-span-2 mt-2">{{ detailRequestCustom.created_at }}</p>
                
                <p class="col-span-1 mt-2">Api Path:</p>
                <p class="col-span-2 mt-2">{{ detailRequestCustom.api_path }}</p>
                  
                <p class="col-span-1 mt-2">Status:</p>
                <el-check-tag
                  v-if="detailRequestCustom?.status == 'Success'"
                  class="!text-[#72c749] !bg-[#f0f9eb] col-span-2 mt-2"
                >
                  {{ detailRequestCustom?.status }}
                </el-check-tag>
                <el-check-tag v-else class="!text-[#f67d7d] !bg-[#fef0f0] col-span-2 mt-2">
                  {{
                    detailRequestCustom?.status
                  }}
                </el-check-tag>
              </div>
            </el-tab-pane>
            <el-tab-pane label="OCR" name="ocr" class="!text-base">
              <p class="text-[16px] p-5 whitespace-pre-line">{{ ocrText }}</p>
            </el-tab-pane>
            <el-tab-pane label="Extracted" name="extracted" class="!text-base">
              <JsonViewer
                :data-view-json="dataResults"
              />
            </el-tab-pane>
            <el-tab-pane label="File" name="file" class="!text-base">
              <div v-if="contentType == 'image'">
                <ImgViewer :url="url" />
              </div>
              <div v-else>
                <PdfViewerAfterUpload :url="url" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<style>
.main-content {
  min-height: 100vh;
  background: #fff;
  padding: 20px;
}

.card {
  box-shadow: 0px 0px 12px rgb(0 0 0 / 12%);
  border-radius: 10px;
  padding: 25px;
  flex-basis: 23%;
}

.card.success {
  border-left: 3px solid rgb(80, 135, 236);
  color: rgb(80, 135, 236);
}

.card.all-request {
  border-left: 3px solid rgb(80 183 236);
  color: rgb(80 183 236);
}

.card.client-error {
  border-left: 3px solid rgb(236 137 80);
  color: rgb(236 137 80);
}

.card.system-error {
  border-left: 3px solid rgb(236 80 80);
  color: rgb(236 80 80);
}

.el-pagination > .is-first {
  position: absolute !important;
  left: 0 !important;
}

.el-dropdown__popper:has(
    > .el-scrollbar > .el-scrollbar__wrap > .el-scrollbar__view > .el-filter-status
  ) {
  width: 320px;
}

.update-post-check {
  margin-right: 0px;
  width: 40% !important;
  height: 100vh;
  max-height: 100vh;
}

.el-overlay-dialog:has(> .update-post-check) {
  left: auto;
  bottom: auto;
  width: 100% !important;
}

.update-post-check .el-dialog__header {
  margin-right: 0px;
  display: flex;
  align-items: center;
  padding: 22px 20px !important;
}

.update-post-check .el-dialog__header .el-dialog__close {
  font-size: 24px;
  color: #8f9296;
}

.update-post-check .el-dialog__header .el-dialog__title {
  font-size: 20px;
  color: #8f9296;
}

.custom-dialog {
  animation: slideRight 0.5s forwards;
}

.closing {
  animation: slideLeft 0.5s forwards;
}

@keyframes slideRight {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(0%);
  }

  to {
    transform: translateX(100%);
  }
}

.post-id:hover {
  color: #339cea;
  font-weight: 700;
}

.el-dialog__body:has(> .post-check-tabs) {
  padding: 30px 20px;
  position: relative;
}

.el-dialog__body .el-tabs__item {
  font-size: 17px;
}
</style>
