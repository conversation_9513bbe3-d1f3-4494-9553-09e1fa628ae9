<template>
  <div>
    <JsonViewer v-if="dataResults" copyable theme="dark" :expand-depth="50" :value="dataResults" />
  </div>
</template>

<script setup lang="ts">
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'
import { getPostCheckById } from '@lib/api'
import { ref } from 'vue';

const dataResults = ref([])

const getRequestDetail = async (id: string) => {
  await getPostCheckById(id).then((res) => {
    dataResults.value = res?.data?.data?.data?.result
  })
}

const path_name = new URL(location.href).pathname
const convert_path = path_name.split('/')
const request_id = convert_path[2]
await getRequestDetail(request_id)
</script>
