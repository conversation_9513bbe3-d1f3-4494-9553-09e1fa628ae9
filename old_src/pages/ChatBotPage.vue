<script setup lang="ts">
import ChatBar from '@components/ChatBar.vue';
import MainChatWindow from '@components/MainChatWindow.vue';
import { chat, createSession, getSessionHistory } from '@lib/api';
import { AxiosError } from 'axios';
import DOMPurify from 'dompurify';
import { ElMessage } from 'element-plus';
import markdownIt from 'markdown-it';
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

interface IConversation {
  role: 'user' | 'assistant'
  content: string
}

const route = useRoute()
const router = useRouter()
const sessionId = ref<string>(route.params.id as string)
const conversations = ref<IConversation[]>([{role: 'assistant', content: 'Hi! Please ask me anything to start a new chat.'}]);
const prompt = ref<string>('')
const isStreaming = ref<boolean>(false)

const chatWindowRef = ref<InstanceType<typeof MainChatWindow> | null>(null)
const chatBlock = ref<HTMLDivElement|null>(null)

const createNewSession = async () => {
  const res = await createSession()
  if (res.status !== 200) {
    ElMessage({ message: 'Failed to create new session', type: 'error', grouping: true })
    return
  }
  sessionId.value = res.data.data.id
}

const submit = async () => {
  if (!sessionId.value) await createNewSession()
  if (prompt.value) {
    conversations.value.push({role: 'user', content: prompt.value})
    await getResponse()
    router.push(`/chat/${sessionId.value}`)
  }
}

const formatResponse = (text: string) => {
  const md = new markdownIt({
    linkify: true,
    typographer: true,
  })
  const html =  md.render(text)
  const sanitized = DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['ol', 'ul', 'li', 'b', 'i', 'strong', 'em', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'target', 'title', 'alt'],  // Add any attributes you need
  })
  return sanitized
}

const getResponse = async () => {
  chatWindowRef.value?.scrollToBottom()
  isStreaming.value = true
  conversations.value.push({role: 'assistant', content: 'Thinking...'})
  prompt.value = ''
  await getStreamingResponse(conversations.value)
}

const getStreamingResponse = async (conversation: IConversation[]) => {
  let res = ''
  let assistantIndex: number | null = null;
  try {
    const real_history = [...conversation]
    real_history.pop() // Remove the last 'Thinking...' message
    const response = await chat(sessionId.value, real_history)
    const reader = response.data.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = '';
    while (isStreaming.value) {
      const { done, value } = await reader.read();
      if (done) {
        isStreaming.value = false;
        break;
      }
      buffer += decoder.decode(value, { stream: true });

      const lines = buffer.split("\n\n");
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const jsonString = line.replace("data: ", "").trim();

          if (jsonString === "[DONE]") {
            isStreaming.value = false;
            return;
          }

          try {
            const parsed = JSON.parse(jsonString);
            const delta = parsed.choices?.[0]?.delta?.delta?.content || '';
            console.log('delta', delta)
            res += delta;

            if (assistantIndex === null) {
              assistantIndex = conversations.value.length - 1;
              conversations.value[assistantIndex].content = '';
            }
            conversations.value[assistantIndex].content = formatResponse(res);
            chatWindowRef.value?.scrollToBottom()
          } catch (err) {
            console.error("JSON parse error:", err);
            continue;
          }
        }
      }
    }
  } catch (error) {
    if (error instanceof AxiosError && error.response) {
      if ([401].includes(error.response.status)) {
        ElMessage({ message: 'Expired Token!', type: 'error', grouping: true })
      }
      else if (error.code == 'ERR_NETWORK') {
        ElMessage({ message: 'No connection to server', type: 'error', grouping: true })
      }
      else if ([500].includes(error.response.status)) {
        ElMessage({ message: 'Internal Server Error', type: 'error', grouping: true })
      }
      else {
        ElMessage({ message: error.response.data.message, type: 'error', grouping: true })
      }
    }
  }
}

const handleResize = () => {
  chatWindowRef.value?.scrollToBottom()
}

const getHistory = async () => {
  const res = await getSessionHistory(sessionId.value)
  if (res.status !== 200) {
    ElMessage({ message: 'Failed to fetch session history', type: 'error', grouping: true })
    return
  }
  conversations.value = res.data.data.map((item: IConversation) => ({
    role: item.role,
    content: formatResponse(item.content)
  }))
}

onMounted(async () => {
  if (sessionId.value) await getHistory()
  chatWindowRef.value?.scrollToBottom()
})

watch(
  () => route.params.id,
  async (newSessionId: string | string[], oldSessionId: string | string[]) => {
    if (newSessionId && newSessionId !== oldSessionId) {
      sessionId.value = newSessionId as string
      await getHistory()
      chatWindowRef.value?.scrollToBottom()
    } else if (!newSessionId) {
      sessionId.value = ''
      conversations.value = [{role: 'assistant', content: 'Hi! Please ask me anything to start a new chat.'}]
    }
  }
)

</script>

<template>
  <div
    class="fixed-height-page relative w-full flex flex-col"
  >
    <div ref="chatBlock" class="flex-1 overflow-hidden relative">
      <MainChatWindow 
        ref="chatWindowRef" 
        v-model:conversations="conversations"
      />
    </div>

    <div class="grid grid-cols-21 min-h-[100px] h-auto py-5">
      <div class="col-span-12 col-start-5">
        <ChatBar v-model:prompt="prompt" :is-streaming="isStreaming" @submit="submit" @resize-textarea="handleResize" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.fixed-height-page {
  height: calc(100vh - 25px);
  z-index: 2000;
}
</style>