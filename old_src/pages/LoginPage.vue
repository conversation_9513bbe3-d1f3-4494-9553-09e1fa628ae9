<script setup lang="ts">
import { handleLoginProcess } from '@lib/api';
import { i18n } from '@lib/i18n';
import { useAuthStore } from '@store/auth.store';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import { z } from 'zod';

const authStore = useAuthStore()

const setMode = ref(true)

const isValidated = ref(false)

const formSchema = z.object({
  email: z.string().email(),
  password: z.string().trim(),
})

const formInput = reactive({
  email: '',
  password: ''
})

const validateForm = (
  rule: { field: string },
  _value: unknown,
  cb: (message?: string) => unknown,
) => {
  const validate = formSchema.safeParse(formInput)

  if (!validate.success) {
    isValidated.value = false

    const error = validate.error.issues.find(
      (error) => error.path[0] === rule.field,
    )

    if (error) {
      return cb(i18n.t(error.message, { defaultValue: 'Invalid input' }))
    }

    return cb()
  }

  isValidated.value = true
  cb()
}

const rules = reactive({
  email: [
    { validator: validateForm, required: true, trigger: ['change', 'blur'] }
  ],
  password: [
    { validator: validateForm, required: true, trigger: ['change', 'blur'] }
  ]
})
const loading = ref(false)

const formRef = ref<HTMLFormElement|null>(null)

const handleLogin = async () => {
  loading.value = true
  const formData = formInput
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) {
      loading.value = false
    } else {
        await handleLoginProcess(formData.email, formData.password)
        .then((res) => {
          if (res?.status == 200) {
            ElMessage({ message: 'Login successfully!', type: 'success', grouping: true })
            loading.value = false
            localStorage.setItem('access_token', res?.data?.data?.access_token)
            localStorage.setItem('refresh_token', res?.data?.data?.refresh_token)
            authStore.login()
          }
        })
        .catch((error) => {
          if ([401].includes(error.response.status)) {
            ElMessage({ message: 'Incorrect email or password!', type: 'error', grouping: true })
            loading.value = false
          }
          else if (error.code == 'ERR_NETWORK') {
            ElMessage({ message: 'No connection to server', type: 'error', grouping: true })
            loading.value = false
          }
          else if ([500].includes(error.response.status)) {
            ElMessage({ message: 'Internal Server Error', type: 'error', grouping: true })
            loading.value = false
          }   
          else {
            ElMessage({ message: 'Unexpected error!', type: 'error', grouping: true })
            loading.value = false
          }
        })
      }
  })
}

</script>

<template>
  <div class="login-layout h-screen flex w-full flex-col items-center justify-center lg:flex-row">
    <div class="!bg-[#052849] w-full h-screen lg:w-1/2 flex flex-col items-center">
      <p class="!text-[#3cb4e7] text-[3rem] font-semibold mt-[19%] mb-[5%] text-center">RABI SMART VISION</p>
      <img class="lg:block hidden h-auto w-[50%]" src="/Smartvision.webp" alt="" />
    </div>
    <div
      class="w-full h-screen flex justify-center max-lg:items-center lg:w-1/2"
      :class="[setMode ? '!bg-[#ffffff]' : '!bg-[#121212]']"
    >
      <div class="min-w-[350px] lg:mt-[19%]">
        <p
          class="text-[3rem] font-semibold text-center"
          :class="[setMode ? '!text-[#595a5c]' : '!text-[#ffffff]']"
        >
          LOGIN
        </p>
        <el-form
          ref="formRef"
          :model="formInput"
          :rules="rules"
          label-position="top"
          class="mt-4 !w-[400px] login-form"
          status-icon
          label-width="auto"
          @keyup.enter.prevent="handleLogin"
        >
          <el-form-item prop="email">
            <span class="!text-[red]">*</span>
            <p :class="[setMode ? '!text-[#595a5c]' : '!text-[#ffffff]']">Email</p>
            <el-input v-model="formInput.email" size="large" class="w-360px" placeholder="" />
          </el-form-item>
          <el-form-item prop="password">
            <span class="!text-[red]">*</span>
            <p :class="[setMode ? '!text-[#595a5c]' : '!text-[#ffffff]']">Password</p>
            <el-input v-model="formInput.password" size="large" type="password" placeholder="" />
          </el-form-item>
          <el-button
            class="w-full !text-white !h-[40px] !font-semibold !mt-[6px]"
            color="#409eff"
            :loading="loading"
            @click.prevent="handleLogin()"
          >
            Login
          </el-button>
          <div class="flex justify-end">
            <div class="relative">
              <input id="switch" v-model="setMode" type="checkbox" /><label for="switch">Toggle</label>
              <i v-if="setMode" class="ri-sun-line absolute top-[23px] right-[5px]"></i>
              <i v-if="!setMode" class="ri-moon-line absolute top-[23px] left-[4px]"></i>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style>
.login-form .el-form-item__error {
  margin-left: 0 !important;
}
</style>
<style scoped>
input[type='checkbox'] {
  height: 0;
  width: 0;
  visibility: hidden;
}

label {
  cursor: pointer;
  text-indent: -9999px;
  width: 60px;
  height: 25px;
  background: grey;
  display: block;
  border-radius: 100px;
  position: relative;
}

label:after {
  content: '';
  position: absolute;
  left: 0px;
  width: 25px;
  height: 25px;
  background: #fff;
  border-radius: 99%;
  transition: 0.1s;
}

input:checked + label {
  background: #409eff;
}

input:checked + label:after {
  left: calc(100% - 0.5px);
  transform: translateX(-100%);
}
</style>
