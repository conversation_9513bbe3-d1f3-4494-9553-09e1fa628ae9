import '@assets/_variable.css'
import '@assets/main.css'

import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
// import { Vue3Mq } from 'vue3-mq'

// styles
import 'element-plus/dist/index.css'
import 'remixicon/fonts/remixicon.css'

// components
import App from './App.vue'
import router from './router'

const app = createApp(App)
const pinia = createPinia()

app.use(router)
app.use(pinia)
app.use(ElementPlus)
// app.use(Vue3Mq, {
//   preset: 'bootstrap5'
// })

app.mount('#app')
