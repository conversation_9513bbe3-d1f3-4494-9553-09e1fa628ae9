<script setup lang="ts">
import ImgViewer from '@components/ImgViewer.vue';
import { ref, watch } from 'vue';


const isScrollBtnVisible = ref<boolean>(false)
const chatWindowRef = ref<HTMLDivElement|null>(null)

interface IConversation {
  role: 'user' | 'assistant' | 'image'
  content: string
}

const conversations = defineModel<IConversation[]>("conversations", { default: [] })

const scrollToBottom = () => {
  setTimeout(() => {
    if (chatWindowRef.value) {
      chatWindowRef.value.scroll({
        top: chatWindowRef.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, 50)
}

const handleScroll = () => {
  if (chatWindowRef.value) {
    isScrollBtnVisible.value = chatWindowRef.value.scrollTop + 100 <= chatWindowRef.value.scrollHeight - chatWindowRef.value.clientHeight
  }
}

watch(
  () => conversations.value,
  () => {
    if (chatWindowRef.value && conversations.value.length >= 2) {
      chatWindowRef.value.addEventListener('scroll', handleScroll)
    }
  },
  { deep: true }
)

defineExpose({ scrollToBottom })
</script>

<template>
  <div ref="chatWindowRef" class="grid grid-cols-21 gap-2 overflow-auto h-full relative">
    <div class="pt-5 col-start-5 col-span-12 h-full">
      <div class="flex-1 h-full w-full flex flex-col justify-start text-white text-lg bg-[#18191B] pt-10">
        <div v-for="(conversation, index) in conversations" :key="index" class="w-full mb-8">
          <div v-if="conversation.role == 'assistant'" class="bg-transparent w-full">
            <div class="response-text prose prose-invert prose-dark prose-maxWidth" v-html="conversation.content"></div>
          </div>
          <div v-else class="w-full flex justify-end">
            <div class="max-w-[70%]">
              <div v-if="conversation.role == 'image'" class="pb-3 w-full">
                <ImgViewer :url="conversation.content" :max-height="200" style="border: 10px solid #2F2E2E; border-radius: 8px;" />
              </div>
              <div v-else class="flex justify-end w-full">
                <div class="inline-block bg-[#282C2F] rounded-lg py-2 px-3">
                  <div class="response-text prose prose-invert prose-dark" v-html="conversation.content"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> 
  <div v-if="isScrollBtnVisible" class="scroll-bottom">
    <i class="ri-arrow-down-line scroll-bottom-btn" @click="scrollToBottom"></i>
  </div>
</template>

<style scoped>
.scroll-bottom {
  position: absolute;
  z-index: 500;
  bottom: 25px;
  right: 50%;
  transform: translateX(50%);
}

.scroll-bottom-btn {
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border: solid 1px #ccc;
  border-radius: 50%;
  background-color: #2F2E2E;
}

.custom-style .el-segmented {
  --el-segmented-item-selected-color: white;
  --el-segmented-item-selected-bg-color: #0082BE;
}

:deep(.response-text *) {
  font-size: 18px;
}
</style>