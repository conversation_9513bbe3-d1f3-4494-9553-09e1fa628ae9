<template>
  <article :class="{'user': role ==='user', 'assistant': role === 'assistant', 'chat-message-item-wrapper': true}" v-html="html"></article>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import {md} from '@/utils/markdown';

interface MessageProps {
  id: string;
  content: string;
  role:  'user' | 'assistant';
}

const props = withDefaults(defineProps<MessageProps>(), {})

const html = computed(() => md.render(props.content));

</script>

<style lang="scss" scoped>
.user {
  text-align: right;
}

.assistant {
  p {
    margin: 0;
    margin-block-start: 0 !important;
    margin-block-end: 0 !important;
  }

  pre {
    background: #282c34;
    color: #eee;
    padding: 12px;
    border-radius: 6px;
  }
}

.assistant p {
  margin: 0;
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
}
</style>