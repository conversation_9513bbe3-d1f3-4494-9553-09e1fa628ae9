<script setup lang="ts">
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'

const props = defineProps({
  dataViewJson: {
    type: Object,
    default() {
      return {}
    }
  }
})
</script>

<template>
  <div class="box-json">
    <JsonViewer
      v-if="props.dataViewJson.length != 0"
      :value="props.dataViewJson"
      copyable
      boxed
      theme="light"
      :expand-depth="3"
    />
  </div>
</template>

<style scoped>
.box-json {
  margin-top: 1rem;
  height: calc(100vh - 350px);
  overflow: auto;
}
</style>
