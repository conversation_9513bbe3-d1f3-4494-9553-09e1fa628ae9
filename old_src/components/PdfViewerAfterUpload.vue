<script setup lang="ts">
import { ref, watch } from 'vue'
import { VuePDF, usePDF } from '@tato30/vue-pdf'

const props = defineProps({
  url: {
    type: String,
    default() {
      return ''
    }
  },
  width: {
    type: [Number, String],
    default() {
      return 0
    }
  },
})

const scale = ref(1)
const rotation = ref(0)
let { pdf, pages } = usePDF(props.url)

watch(
  () => props.url,
  (newUrl) => {
    ({ pdf, pages } = usePDF(newUrl));
  }
)

</script>

<template>
  <div class="flex flex-col h-full">
    <div class="view-pdf__header grid grid-cols-12 gap-2 h-[70px] items-center bg-white">
      <div
        class="px-[25px] py-[5px] border rounded-[20px] border-[#00000070] cursor-pointer text-center"
        :class="Number(width) > 500 ? 'col-start-3' : 'col-start-2 col-span-1'"
        @click="scale = scale > 0.25 ? scale - 0.25 : scale"
      >
        -
      </div>
      <div class="px-[18px] col-start-4">{{ scale * 100 }}%</div>
      <div
        class="px-[25px] py-[5px] border rounded-[20px] border-[#00000070] cursor-pointer text-center"
        :class="Number(width) > 500 ? 'col-start-5' : 'col-start-6 col-span-1'"
        @click="scale = scale < 2 ? scale + 0.25 : scale"
      >
        +
      </div>
      <div
        class="px-[25px] py-[5px] border rounded-[20px] border-[#00000070] cursor-pointer"
        :class="Number(width) > 500 ? 'col-start-9' : 'col-start-8 col-span-1'"
        @click="rotation = rotation - 90"
      >
        <i class="ri-arrow-go-back-line"></i>
      </div>
      <div
        class="px-[25px] py-[5px] border rounded-[20px] border-[#00000070] cursor-pointer"
        :class="Number(width) > 500 ? 'col-start-10' : 'col-start-11 col-span-1'"
        @click="rotation = rotation + 90"
      >
        <i class="ri-arrow-go-forward-line"></i>
      </div>
    </div>
    <div
      class="flex flex-1 flex-col overflow-auto h-full items-center"
      style="background: #ccc"
    >
      <div v-for="page in pages" :key="page" class="shadow p-[10px] wrap-page-pdf">
        <VuePDF :pdf="pdf" :page="page" :scale="scale" :rotation="rotation" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-pdf__header {
  border-bottom: 1px solid #ccc;
}
.wrap-page-pdf > div {
  box-shadow: 1px 1px 12px rgb(0 0 0);
}
</style>
