<script setup lang="ts">
import { handleLogoutProcess } from '@lib/api';
import { useAuthStore } from '@src/store/auth.store';

const props = defineProps({
  collapse: { type: Boolean, default: false }
})

const authStore = useAuthStore()

const handleLogOut = async () => {
  await handleLogoutProcess().then(() => {
    authStore.logout()
  })
}
</script>

<template>
  <div :class=" ['fixed-header bg-transparent', { collapsed: props.collapse }] ">
    <div
      class="h-[55px] grid items-center main-header grid-cols-21"
      style="margin: 0 !important"
    >
      <div target="_blank" class="text-[#fff] col-span-3 font-bold ml-[35px]">RABI AI</div>
      <div class="col-start-21 col-span-1 justify-self-end">
        <el-dropdown trigger="click" class="h-full items-end justify-end">
          <div class="el-dropdown-link flex items-center justify-center text-white">
            <div class="flex items-center">
              <i class="ri-account-pin-circle-line mr-5 !text-[25px] text-[#fff]"></i>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="w-48">
              <div class="p-[5px] rounded-lg border-0" @click="handleLogOut">
                <el-dropdown-item>
                  <div class="flex items-center">
                    <i class="ri-logout-box-line"></i>
                    <span class="whitespace-nowrap">Logout</span>
                  </div>
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style scoped>
.admin-layout > .el-header .el-breadcrumb {
  font-size: unset !important;
}

.parent-layout > .el-header .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link,
.admin-layout > .el-header .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  color: white !important;
}

.admin-layout > .el-header .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__inner a.is-show {
  color: #fad02c !important;
}

.admin-layout > .el-header .el-breadcrumb .el-breadcrumb__item .el-breadcrumb__inner a:hover {
  font-weight: unset !important;
}

.admin-layout > .el-header .el-breadcrumb .el-breadcrumb__item:last-child .el-breadcrumb__inner a {
  font-weight: normal !important;
}


.main-header > a:hover {
  text-decoration: underline;
  color: #005eebe3;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 245;
  width: calc(100% - 245px); /* Adjust this according to your sidebar width */
  z-index: 200;
}
.fixed-header.collapsed {
  width: calc(100% - 50px) !important;
  margin-left: 8px;
}
</style>
