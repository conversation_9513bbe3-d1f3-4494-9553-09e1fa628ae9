<script setup lang="ts">
import { ref, watch } from 'vue';

const emit = defineEmits(['submit', 'changeFile', 'resizeTextarea'])

const props = defineProps({
  isStreaming: {
    type: Boolean,
    default() {
      return false
    }
  }
})

const promptTextarea = ref<HTMLTextAreaElement|null>(null)
const prompt = defineModel<string>("prompt", { default: ''})

const submit = () => {
  emit('submit')
  if (promptTextarea.value) promptTextarea.value.style.height = '40px'
}

const handleKeyEnterDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    if (!props.isStreaming) submit()
  }
}

const resizePromptTextarea = () => {
  const element = promptTextarea.value
  if (element) {
    element.style.height = '40px'
    if (element.scrollHeight > 300) {
      element.style.height = '300px'
      element.style.overflowY = 'auto'
    } else {
      element.style.height = element?.scrollHeight + 'px'
      element.style.overflowY = 'hidden'
    }
    emit('resizeTextarea')
  }
}

watch(() => prompt, resizePromptTextarea)

</script>

<template>
  <div class="bg-[#08080B] rounded-[35px] w-full">
    <div class="grid grid-cols-12 gap-2 items-center py-2">
      <div class="flex flex-1 flex-col col-span-11">
        <textarea
          id="promptTextArea"
          ref="promptTextarea"
          v-model="prompt"
          class="w-full input ml-6"
          rows="1"
          placeholder="Input your question"
          @input="resizePromptTextarea"
          @keydown="handleKeyEnterDown"
        >
        </textarea>
      </div>
      <div class="flex items-end h-full justify-end px-[35%] mb-3">
        <button
          class="w-10px"
          title="Send"
          :disabled="isStreaming"
          @click.prevent="submit"
        >
          <i class="ri-send-plane-2-line text-white text-xl pl-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.input {
  height: 40px;
  max-height: 20dvh;
  background-color: transparent;
  color: white;
  resize: none;
  border: none;
  padding-top: 0.5rem;
  padding-right: 5px;
  overflow-y: hidden;
}

.input:focus {
  outline: none;
}
</style>