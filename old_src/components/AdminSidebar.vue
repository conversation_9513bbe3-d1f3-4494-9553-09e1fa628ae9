<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { deleteSession, getAllSessions, updateSessionName } from '@lib/api'
import { MENUS } from '@store/menus'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus';


interface ISession {
  id: string
  session_name: string
  created_at: string
}

interface SubMenu {
  label: string
  route: string
  pathActive: string
}

interface MenuItem {
  label: string
  icon: string
  route: string
  pathActive: string
  subMenus?: SubMenu[]
}

const emit = defineEmits(['zoom-out-sidebar', 'change-collapse'])

const router = useRouter()
const menus = ref<MenuItem[]>(MENUS)
const collapseAside = ref(true)
const defaultActive = ref('')
const pathSubmenu = ref<string[]>()
const pathSubmenuItem = ref('')

const getCurrentUrl = async () => {
  const res = await getAllSessions()
  if (res.status !== 200) {
    ElMessage({ message: 'Failed to fetch sessions', type: 'error', grouping: true })
    return
  }
  const sessions = res.data.data
  if (menus.value[1]?.subMenus) menus.value[1].subMenus = []
  sessions.forEach((session: ISession) => {
    if (menus.value[1]?.subMenus) {
      menus.value[1].subMenus.push({
        label: session?.session_name || 'New Chat',
        route: `/chat/${session.id}`,
        pathActive: `/chat/${session.id}`
      })
    } else {
      menus.value[1].subMenus = []
      menus.value[1].subMenus.push({
        label: session?.session_name || '',
        route: `/chat/${session.id}`,
        pathActive: `/chat/${session.id}`
      })
    }
  })
  const pathname = window.location.pathname.split('/')
  const menu_map = menus.value.map((item) => item.pathActive)
  let currentUrl = pathname.filter((element) => menu_map.includes(element)).toString()
  defaultActive.value = currentUrl || 'dashboard'
  pathSubmenu.value = window.location.pathname.split('/')
  if (pathSubmenu.value[2] == 'my-account') {
    defaultActive.value = ''
  }
  pathSubmenuItem.value = window.location.pathname?.split('/')?.slice(0, 4).join('/')
  console.log('menus', menus.value)
}

const toggleCollapse = () => {
  collapseAside.value = !collapseAside.value
  emit('change-collapse', collapseAside.value)
}
const checkActive = (pathActive: string, hasSubMenus: boolean = true) => {
  return hasSubMenus ? pathActive == pathSubmenuItem.value[1] : pathActive == pathSubmenuItem.value
}

onMounted(() => {
  getCurrentUrl()
})

const goHome = () => {
  router.push('/')
}

const route = useRoute()
watch(
  () => route.fullPath,
  () => {
    getCurrentUrl()
  }
)
</script>

<template>
  <el-aside
    class="aside fixed bottom-0 left-0 z-[500] top-0"
    :class="{ 'collapse-is-close': !collapseAside }"
  >
    <div class="relative h-full bg-[#fff]">
      <!-- Side bar -->
      <div class="admin-sidebar h-full relative">
        <div class="logo h-[55px] flex items-center justify-center">
          <img class="w-[80%] cursor-pointer" src="/src/assets/images/logo.png" alt="RABI AI" @click="goHome" />
        </div>
        <el-menu
          id="sidebar"
          class="el-menu-vertical"
          :class="{ 'collapse-is-close': !collapseAside }"
          :default-active="defaultActive"
          :default-openeds="['/chat']"
          :unique-opened="true"
          :router="true"
          :collapse="!collapseAside"
        >
          <template v-for="(menu, index) in menus" :key="index">
            <div>
              <RouterLink v-if="!menu.subMenus" :to="menu.route">
                <el-menu-item
                  class="menu-item-custom"
                  :index="menu.pathActive"
                  :class="checkActive(menu.pathActive, false) ? 'is-active-admin' : ''"
                  @click="getCurrentUrl()"
                >
                  <i class="text-[18px]" :class="menu.icon"></i>
                  <template #title>
                    <span class="menu-item-text pl-2 text-[16px]">{{ menu.label }}</span>
                  </template>
                </el-menu-item>
              </RouterLink>

              <el-sub-menu
                v-else
                :index="menu.pathActive"
                class="menu-item-custom"
                :class="checkActive(menu.pathActive) ? 'is-active-admin ' : ''"
                popper-class="border border-[#d0d5dd]"
              >
                <template #title>
                  <i class="text-[16px]" :class="menu.icon"></i>
                  <span class="menu-item-text pl-2">{{ menu.label }}</span>
                </template>
                <div class="submenu-wrapper">
                  <div class="submenu-inner">
                    <template v-for="(subMenu, i) in menu.subMenus" :key="i">
                      <el-menu-item
                        :index="subMenu.pathActive"
                        class="flex items-center rounded-xl group"
                        :class="
                          subMenu.pathActive == pathSubmenuItem
                            ? 'is-active-admin'
                            : ''
                        "
                        @click="getCurrentUrl()"
                      >
                        <template #title>
                          <RouterLink class="menu-item-text pl-5 block w-[90%]" :to="subMenu.route">
                            <span>{{ subMenu.label }}</span>
                          </RouterLink>
                          <!-- <el-tooltip placement="right-start" effect="dark" content="More options">
                            <template #content>
                              <div class="flex gap-2 flex-col justify-start items-start w-[150px]">
                                <el-button class="w-full h-10 !m-0" link @click.stop="updateSessionName">
                                  <i class="ri-edit-2-line text-[16px] w-[20%]"></i>
                                  <span class="w-[80%]">Rename</span>
                                </el-button>
                                <el-button class="w-full !m-0" link @click.stop="deleteSession">
                                  <i class="ri-delete-bin-line text-[16px] pr-1"></i>
                                  Delete
                                </el-button>
                              </div>
                            </template>
                            <el-button class="opacity-0 group-hover:opacity-100 transition-opacity" type="plain" link @click.stop="">
                              <i class="ri-more-line text-[16px] pr-2"></i>
                            </el-button>
                          </el-tooltip> -->
                        </template>
                      </el-menu-item>
                    </template>
                  </div>
                </div>
              </el-sub-menu>
            </div>
          </template>
        </el-menu>
      </div>

      <div class="absolute top-[7px] right-[-14px]">
        <button v-if="!collapseAside" @click="toggleCollapse">
          <span v-if="!collapseAside" class="text-[#fff]">
            <i
              class="ri-arrow-right-circle-line text-[27px] bg-[#120c6a]"
              style="border-radius: 50%"
            ></i>
          </span>
        </button>

        <button v-else @click="toggleCollapse">
          <span class="text-[#fff]">
            <i
              class="ri-arrow-left-circle-line text-[27px] bg-[#120c6a]"
              style="border-radius: 50%"
            ></i>
          </span>
        </button>
      </div>
    </div>
  </el-aside>
</template>

<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 245px;
}

</style>
