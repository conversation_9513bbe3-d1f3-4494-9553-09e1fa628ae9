<script setup lang="ts">
import Panzoom from '@panzoom/panzoom'
import { ElCarousel } from 'element-plus'
import { ComponentPublicInstance, PropType, onMounted, ref } from 'vue'

interface INewFile extends File {
  typeFile: string
  url: string
}

const emit = defineEmits(['changeActiveIndex'])


const props = defineProps({
  url: {
    type: String,
    default() {
      return ''
    }
  },
  zoom: {
    type: Boolean,
    default() {
      return false
    }
  },
  imageList: {
    type: Array as PropType<INewFile[]>,
    default() {
      return []
    }
  },
  maxHeight: {
    type: Number,
    default() {
      return -1
    }
  }
})

const panzoom = ref()
const imageSelect = ref<InstanceType<typeof ElCarousel>|null>(null)
const imgRefs = ref<HTMLElement[]>([])

const setItemRef = (el: Element|ComponentPublicInstance|null) => {
  if (el) {
    const element = el as HTMLElement
    if (!imgRefs.value.some(item => item.title == element.title)) imgRefs.value.push(element)
  }
}

const handleActiveImage = (index: number) => {
  imageSelect.value?.setActiveItem(index)
}

onMounted(() => {
  if (imgRefs.value[0]) {
    panzoom.value = Panzoom(imgRefs.value[0])
  }
})

const changePanzoom = (current: number) => {
  if (imgRefs.value) {
    panzoom.value = Panzoom(imgRefs.value[current])
  }

  emit('changeActiveIndex', current)
}

const zoomIn = () => {
  panzoom.value.zoomIn()
}

const zoomOut = () => {
  panzoom.value.zoomOut()
}

const zoomReset = () => {
  panzoom.value.reset()
}

defineExpose({ handleActiveImage })
</script>
<template>
  <div v-if="zoom" class="h-full w-full flex flex-col items-center justify-center">
    <div class="h-[95%] w-full px-2">
      <el-carousel
        ref="imageSelect"
        height="65vh"
        :autoplay="false"
        :indicator-position="'none'"
        :arrow="props.imageList.length > 1 ? 'hover' : 'never'"
        :loop="false"
        @change="changePanzoom"
      >
        <el-carousel-item
          v-for="(item, index) in props.imageList"
          :key="index"
          class="!flex items-center"
          :name="item.name"
        >
          <img :ref="el => setItemRef(el)" :title="item.name" :src="item.url" alt="" class="img-zoom" />
        </el-carousel-item>
      </el-carousel>
    </div>
    <div v-if="imageList.length > 0" class="w-full grid grid-cols-8 gap-1 items-center py-[20px]">
      <el-button type="primary" class="col-span-2 col-start-2" color="#0082BE" @click="zoomIn">
        Zoom in
      </el-button>
      <el-button type="primary" class="col-span-2 col-start-4" color="#0082BE" @click="zoomOut">
        Zoom out
      </el-button>
      <el-button type="primary" class="col-span-2 col-start-6" color="#0082BE" @click="zoomReset">
        Reset
      </el-button>
    </div>
  </div>
  <div
    v-else
    class="flex justify-center h-full overflow-y-auto"
    style="background: #ccc; padding-top: auto;"
  >
    <div v-if="props.maxHeight == -1" class="grid place-items-center   h-full">
      <img :src="url" alt="" class="img-upload-viewer" />
    </div>
    <img v-else :src="url" alt="" class="img-conversation items-center" :style="`max-height: ${props.maxHeight}px`" />
  </div>
</template>

<style scoped>
.img-upload-viewer {
  object-fit: contain;
}

.img-zoom {
  width: 100%;
  object-fit: contain;
}

.img-conversation {
  max-width: 100%;
  object-fit: contain;
}
</style>
