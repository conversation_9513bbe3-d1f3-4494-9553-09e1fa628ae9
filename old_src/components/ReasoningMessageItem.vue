<template>
  <div class="reasoning-wrapper">
    <div class="reasoning-title" @click="toggle">
      <div>
        <div v-if="loading" class="loader-wrapper">
          <!--                    <div class="loader"></div>-->
          <span class="glow-text">Is Thinking...</span>
        </div>
        <span v-if="!loading">Thought for {{time_range}} seconds</span>
      </div>
      <el-icon v-if="!isDrop" size="24"><i class="ri-arrow-drop-down-line" ></i></el-icon>
      <el-icon v-if="isDrop" size="24"><i class="ri-arrow-drop-up-line"></i></el-icon>
    </div>
    <div ref="messageRef" :class="{'reasoning-messages': true}" style="height: 0;">
      <chat-message-item :id="id" :role="'assistant'" :content="reasoningContent"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import ChatMessageItem from "@/components/conversation/chat/ChatMessageItem.vue";
import {ref} from "vue";

interface ReasoningMessageProps {
  id: string;
  reasoningContent: string;
  loading: boolean;
  time_range: any;
}

const messageRef = ref();
const props = defineProps<ReasoningMessageProps>();
const isDrop = ref(false);

function open() {
  isDrop.value = true;
  messageRef.value.style.height = messageRef.value.scrollHeight + 'px';

  const onEnd = (e: any) => {
    if (e.propertyName !== 'height') return;
    messageRef.value.style.height = 'auto';
    messageRef.value.removeEventListener('transitionend', onEnd);
  };
  messageRef.value.addEventListener('transitionend', onEnd);
}

function close() {
  messageRef.value.style.height = messageRef.value.offsetHeight + 'px';
  messageRef.value.offsetHeight;
  messageRef.value.style.height = '0px';
}
function toggle() {
  isDrop.value = false;
  const isClosed = messageRef.value.style.height === '' || messageRef.value.style.height === '0px';
  if (isClosed) open();
  else close();
}
</script>

<style scoped>
.reasoning-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reasoning-title {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  color: #9B9B9B;
}


.reasoning-messages {
  padding: 0 0 0 20px;
  box-sizing: border-box;
  border-left: 2px #808080 solid;
  transform-origin: top;
  transition: all .35s ease;
  overflow: hidden;

  p{
    margin: 20px;
  }
}

.glow-text {
  position: relative;
  font-weight: bold;
  font-size: 14px;
  background: linear-gradient(
      120deg,
      #fff,
      #959797,
      #fff
  );
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: shimmer 2s infinite linear;
}

@keyframes shimmer {
  0% {
    background-position: 200% center;
  }
  100% {
    background-position: -200% center;
  }
}

.loading-thinking {
  flex-direction: column;
}

.loader-wrapper {
  display: flex;
  gap: 5px;
  align-items: center;
  width: fit-content;
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 20px;
  aspect-ratio: 1;
  display: grid;
  border-radius: 50%;
  background: linear-gradient(0deg,
  rgb(255 255 255 / 50%) 30%,
  #0000 0 70%,
  rgb(255 255 255 /100%) 0
  ) 50% / 8% 100%,
  linear-gradient(
      90deg,
      rgb(255 255 255 /25%) 30%,
      #0000 0 70%,
      rgb(255 255 255 /75%) 0
  ) 50% / 100% 8%;
  background-repeat: no-repeat;
  animation: l23 1s infinite steps(12);
}
.loader::before,
.loader::after {
  content: "";
  grid-area: 1/1;
  border-radius: 50%;
  background: inherit;
  opacity: 0.915;
  transform: rotate(30deg);
}
.loader::after {
  opacity: 0.83;
  transform: rotate(60deg);
}
@keyframes l23 {
  100% {transform: rotate(1turn)}
}

</style>