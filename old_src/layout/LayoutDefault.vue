<script setup lang="ts">
import { ref } from 'vue'

const collapseAside = ref(false)
</script>

<template>
  <div class="admin-layout min-h-screen">
    <div class="admin-layout min-h-screen">
      <el-container>
        <AdminSidebar @change-collapse="collapseAside = !collapseAside" />
        <el-container>
          <el-main
            id="el-main"
            :class="{ 'collapse-is-close': collapseAside }"
            style="background-color: #18191B"
            class="ml-[245px] !p-0"
          >
            <AdminHeader :collapse="collapseAside" />
            <RouterView />
          </el-main>
        </el-container>
      </el-container>
    </div>
  </div>
</template>