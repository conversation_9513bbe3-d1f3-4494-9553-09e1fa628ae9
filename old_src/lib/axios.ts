import ax, { AxiosError, AxiosRequestConfig } from 'axios'

/**
 * Axios default config
 */
export const defaultConfig: AxiosRequestConfig = {
  // `baseURL` will be prepended to `url` unless `url` is absolute.
  // It can be convenient to set `baseURL` for an instance of axios to pass relative URLs
  // to methods of that instance.
  baseURL: import.meta.env.VITE_API_URL,

  // `timeout` specifies the number of milliseconds before the request times out.
  // If the request takes longer than `timeout`, the request will be aborted.
  // default is `0` (no timeout)  
  timeout: 60000,

  // `headers` are custom headers to be sent
  headers: { 'X-Requested-With': 'XMLHttpRequest' },

  // `responseType` indicates the type of data that the server will respond with
  // options are: 'arraybuffer', 'document', 'json', 'text', 'stream'
  //   browser only: 'blob'
  responseType: 'json',

  // `xsrfCookieName` is the name of the cookie to use as a value for xsrf token
  xsrfCookieName: 'XSRF-TOKEN', // default

  // `xsrfHeaderName` is the name of the http header that carries the xsrf token value
  xsrfHeaderName: 'X-XSRF-TOKEN' // default
}

export const useAxios = (options = {}) => {
  const instance = ax.create({ ...defaultConfig, ...options })

  // Add a request interceptor
  instance.interceptors.request.use(
    (requestConfig) => {
      // Do something before request is sent
      const token = localStorage.getItem('access_token')
      if (token) {
        requestConfig.headers.Authorization = `Bearer ${token}`
      }
      return requestConfig
    },
    (error: AxiosError) => {
      // Do something with request error
      return Promise.reject(error)
    }
  )

  // Add a response interceptor
  instance.interceptors.response.use(
    (response) => {
      // Any status code that lie within the range of 2xx cause this function to trigger
      // Do something with response data
      return response
    },
    (error: AxiosError) => {
      // Any status codes that falls outside the range of 2xx cause this function to trigger
    
      // Do something with response error
      return Promise.reject(error)
    }
  )

  return instance
}
