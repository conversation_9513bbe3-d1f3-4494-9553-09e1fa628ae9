import { useAxios } from './axios'

interface IMessage {
  role: 'user' | 'assistant'
  content: string
}

export async function postClassify(file: File) {
  const url = `/ocr/${import.meta.env.VITE_API_VERSION}/classify?document_type=Feuille%20de%20soins`
  const http = useAxios()

  return http.post(
    url,
    {
      document_file: file,
      document_type: '<PERSON><PERSON>le de soins'
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  )
}

export async function getStatisticCounters(from: string, to: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/statistic/requests`
  const http = useAxios()

  return http.post(url, {
    from_date: from,
    to_date: to
  })
}

export async function getStatisticByPath(from: string, to: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/statistic/requests/api-path`
  const http = useAxios()

  return http.post(url, {
    from_date: from,
    to_date: to
  })
}

export async function getStatisticByDate(from: string, to: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/statistic/requests/date`
  const http = useAxios()

  return http.post(url, {
    from_date: from,
    to_date: to
  })
}

export async function getExtractedInfo(
  file_front: File,
  document_type: string,
  language: string
) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/document/extract-info`
  const http = useAxios()
  const formData = new FormData();
  formData.append('input_file', file_front);
  formData.append('language', language);
  formData.append('selected_sub_problem', document_type);

  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}

export async function handleLoginProcess(email: string, password: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/login/access-token`
  const http = useAxios()
  return http.post(
    url,
    {
      email: email,
      password: password
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
}
export async function handleLogoutProcess() {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/logout`
  const http = useAxios()
  return http.post(url)
}
export async function getAllRequest(
  from_date: string,
  to_date: string,
  search: string,
  status: string,
  page: number,
  limit: number
) {
  let url = `/be/${
    import.meta.env.VITE_API_VERSION
  }/requests?from_date=${from_date}&to_date=${to_date}`
  // url = url.replace(/\s/g, '')
  if (search != '' && search != undefined && search != null) {
    url = url + `&api_path=${search}`
  }
  if (status != '' && status != undefined && status != null) {
    url = url + `&status=${status}`
  }
  url = url + `&offset=${page}&limit=${limit}`
  const http = useAxios()
  return http.get(url)
}
export async function getPostCheckById(id: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/requests/${id}`
  const http = useAxios()
  return http.post(url)
}
export async function updateRequestInfo(result_id: string, data: any) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/result/${result_id}`
  const http = useAxios()
  return http.put(url, JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export async function postExtractImage(file: File, type: string) {
  const url = `/ai/${
    import.meta.env.VITE_API_VERSION
  }/ocr/dewarp-document?document_type=${type}`
  const http = useAxios()

  return http.post(
    url,
    {
      document_file: file
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  )
}

export async function getOcrResponse(file: File, language: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/document/ocr`
  const http = useAxios()

  return http.post(url, { input_file: file, language: language }, {
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}

export async function ragUpload(file: File) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/rag/upload_file`
  const http = useAxios()

  return http.post(url, { input_file: file }, {
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}

export async function ragChat(text: string) {
  const url = `/be/${import.meta.env.VITE_API_VERSION}/rag/chat`
  const http = useAxios()

  return http.post(url, { text: text }, {
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}

export async function chat(sessionId: string, messages: IMessage[]) {
  const url = `/${import.meta.env.VITE_API_VERSION}/assistant/chat-with-docs`
  const http = useAxios()

  const data = {
    "session_id": sessionId,
    "messages": messages,
    "collection_name": "duongtt_pvn"
  }

  return http.post(url, JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json'
    },
    adapter: "fetch",
    responseType: "stream",
  })
}

export async function getAllSessions() {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.get(url)
}

export async function getSessionHistory(sessionId: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-messages/${sessionId}`
  const http = useAxios()
  return http.get(url)
}

export async function createSession() {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.post(url)
}

export async function updateSessionName(sessionId: string, updated_name: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}?updated_name=${updated_name}`
  const http = useAxios()
  return http.post(url)
}

export async function deleteSession(sessionId: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}`
  const http = useAxios()
  return http.delete(url)
}
