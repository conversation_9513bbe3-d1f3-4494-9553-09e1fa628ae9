// import { useAuthStore } from '@store/auth.store'
// import { storeToRefs } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

import PageNotFound from '@pages/404NotFound.vue'
// import ExtractPaper from '@src/pages/ExtractPaperPage.vue'
// import ViewRawJson from '@pages/ViewRawJson.vue'
// import Analysis from '@src/pages/AnalysisPage.vue'
// import Login from '@src/pages/LoginPage.vue'
// import PostCheck from '@src/pages/PostCheckPage.vue'
import ChatBot from '@pages/ChatBotPage.vue'

const routes = [
  { path: '/', redirect: { name: 'chat'}},
  // { 
  //   path: '/dashboard',
  //   children: [
  //     { path: 'statistic', component: Analysis, name: 'statistic' },
  //     { path: 'post-check', component: PostCheck, name: 'post-check' },
  //   ]
  // },
  // { path: '/document-extract', component: ExtractPaper, name: 'document-extract' },
  { path: '/chat', component: ChatBot, name: 'chat' },
  { path: '/chat/:id', component: ChatBot, name: 'chat-session' },
  // { path: '/post-check-view-json/:id', component: ViewRawJson, name: 'view-raw-json' },
  // {
  //   path: '/login',
  //   component: Login,
  //   name: 'login',
  //   meta: {
  //     layout: 'LayoutBlank',
  //     isPublic: true
  //   }
  // },
  {
    path: '/:catchAll(.*)',
    component: PageNotFound,
    name: 'pagenotfound',
    meta: {
      layout: 'LayoutBlank',
      isPublic: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// router.beforeEach((to, _, next) => {
//   const authStore = useAuthStore()
//   const { isAuthenticated } = storeToRefs(authStore)

//   if (checkAccessToken()) {
//     if (to.name === 'login') {
//       isAuthenticated.value = true
//       return next({ name: 'statistic' })
//     }
//     return next()
//   }
//   else {
//     isAuthenticated.value = false
//   }

//   const isRestricted = !isAuthenticated.value && !to.meta.isPublic

//   if (isRestricted) {
//     return next({ name: 'login' })
//   }

//   next()
// })

// const checkAccessToken = () => {
//   try {
//     const access_token = localStorage.getItem('access_token')
//     if (access_token !== null && access_token !== undefined) {
//       if (isExpiredToken(access_token)) {
//         localStorage.removeItem('access_token')
//         localStorage.removeItem('refresh_token')
//         return false
//       } else {
//         return true
//       }
//     }
//   } catch (error) {
//     return false
//   }
// }

// const isExpiredToken = (token: string) => {
//   const tokenSub = token.split('.')[1]
//   const tokenPayLoad = JSON.parse(atob(tokenSub))
//   return Math.floor(new Date().getTime() / 1000) >= tokenPayLoad?.exp
// }


export default router
