// utils/markdown.ts
import MarkdownIt, {PluginWithOptions} from 'markdown-it';
import hljs from 'highlight.js';
import mathjax3Plugin from "@/utils/mathjax3-plugin";

export const md = new MarkdownIt({
    highlight(code: string, lang: string): string {
        const valid = lang && hljs.getLanguage(lang);
        const html  = valid
            ? hljs.highlight(code, { language: lang }).value
            : md.utils.escapeHtml(code);
        return buildCodeBlockWrapper(lang, `<pre><code class="hljs ${lang}">${html}</code></pre>`);
    },
})
    .use(mathjax3Plugin)
    .use(metadataPlugin);

function metadataPlugin(md: any) {
    const RE = /^\[\[([^|\]\n]+)\|([^\]\n]+)\]\]/;          // [[label|value]]

    md.inline.ruler.before('emphasis', 'pair', (state: any, silent: any) => {
        const m = RE.exec(state.src.slice(state.pos));
        if (!m) return false;
        if (silent) return true;

        const label = m[1].trim();
        const value = m[2].trim();

        const tok = state.push('c_metadata', '', 0);
        tok.meta = { label, value };

        state.pos += m[0].length;
        return true;
    });

    md.renderer.rules.c_metadata = (tokens: any, idx: any) => {
        const { label, value } = tokens[idx].meta;

        return `
            <span class="c_metadata" data-value="${value}">
              <span>[${md.utils.escapeHtml(label)}]</span>
            </span>`;
    };
}

md.renderer.rules.code_inline = (tokens, idx) => {
    const content = md.utils.escapeHtml(tokens[idx].content);
    const inlineCodeElement = document.createElement('code');
    inlineCodeElement.className = 'inline-code';
    inlineCodeElement.innerHTML = content;
    return inlineCodeElement.outerHTML;
};


const buildCodeBlockWrapper = (lang: string, htmlContent: any) => {
    const wrapperElement = document.createElement('div');
    wrapperElement.className = "hljs-code-wrapper";

    const header = document.createElement('div');
    header.className = "hljs-code-wrapper--header";
    header.innerHTML = `
        <span>${lang}</span>
    `
    if (lang)
        wrapperElement.appendChild(header);
    wrapperElement.innerHTML += htmlContent;

    return wrapperElement.outerHTML;
}