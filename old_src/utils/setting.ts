import { ISetting } from '@src/@types/setting.type'

export const getLocalSetting = (): ISetting => {
  try {
    const localSetting = localStorage.getItem('setting') || '{}'
    const setting: ISetting = JSON.parse(localSetting)

    return setting
  } catch (error) {
    return {}
  }
}

export const changeLocalSetting = (setting: ISetting) => {
  const localSetting = getLocalSetting()

  const updateSetting = { ...localSetting, ...setting }

  localStorage.setItem('setting', JSON.stringify(updateSetting))
}
