@tailwind base;
@tailwind components;
@tailwind utilities;


::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: #CCCCCC;
  border-radius: 10px;
}
::-webkit-scrollbar-thumb:active {
  background-color: #555555;
}

body {
  background-color: #fff;
  font-family: sans-serif;
}

#app {
  padding: 0 10px;
}

.admin-layout {
  font-style: normal;
}

.admin-layout > .el-container {
  min-height: 100vh;
  background-color: rgb(233, 233, 233);
}

.admin-layout .el-aside {
  height: 100vh;
  width: var(--el-aside-width, 250px);
  overflow: unset;
}

.admin-layout .el-menu {
  border-right: none;
  background-color: var(--main-background-sidebar);
}

.admin-layout .el-menu .menu-item-custom {
  color: #ffffffd9;
}

.admin-sidebar {
  max-height: calc(100vh);
  overflow-y: auto;
  background-color: var(--main-background-sidebar);
}

.admin-sidebar .logo {
  font-size: 1.25rem;
  font-weight: 700;
  /* filter: drop-shadow(2px 2px 6px #409eff); */
  line-height: 1.7;
  text-align: center;
  transition: font-size.3s ease-in;
  padding: 10px;
  border-bottom: 1px solid #969696;
}

.admin-layout .collapse-is-close .el-sub-menu:not(.is-active-admin) {
  margin-left: 8px;
}

.admin-layout .el-menu-item,
.admin-layout .el-sub-menu__title {
  --el-menu-item-height: 64px;
  font-weight: 500;
  font-size: 16px;
  color: #ffffffd9 !important;
}

.admin-layout .el-sub-menu.is-opened,
.admin-layout .el-sub-menu.is-opened .submenu-wrapper,
.admin-layout .el-sub-menu.is-opened ul {
  background-color: var(--main-background-sidebar) !important;
}

.el-sub-menu .el-menu-item.is-active {
  color: #2d82ff !important;
}

.admin-layout .el-sub-menu__title:hover {
  background-color: var(--main-background-sidebar-active) !important;
}

.admin-layout .el-sub-menu.menu-item-custom.is-active-admin .el-sub-menu__title {
  color: #ffffffd9 !important;
}

.admin-layout .el-sub-menu__title:hover {
  background-color: var(--main-background-sidebar);
}

.admin-layout .el-menu-item.is-active-admin {
  color: #ffffff !important;
  background-color: var(--main-background-sidebar-active);
}

.admin-layout .el-menu-item:hover {
  background-color: var(--main-background-sidebar-active);
  color: #ffffff;
}

.admin-layout .el-sub-menu.is-active-admin .el-sub-menu__title {
  color: var(--tw-blue-400) !important;
}

.admin-layout .el-sub-menu .el-menu-item {
  height: 34px;
  margin-bottom: 8px;
}

.aside {
  transition: width.3s ease-in;
}

.aside.collapse-is-close {
  width: 55px;
}

.aside.collapse-is-close .logo {
  font-size: 0.5rem;
}

.el-menu--collapse {
  width: 55px !important;
  padding: 0 !important;
}

.el-menu--collapse.collapse-is-close .el-menu-item .el-menu-tooltip__trigger,
.el-menu--collapse.collapse-is-close .el-sub-menu__title.el-tooltip__trigger {
  padding: 0 !important;
  display: flex;
  justify-content: center;
}

.admin-layout .el-menu-item {
  padding-left: 1.25rem !important;
}

.el-menu-vertical .el-submenu__title {
  display: flex;
  align-items: center;
  padding-left: 1.25rem !important;
}

.admin-layout .el-menu-item.is-active-admin {
  color: var(--tw-black-400);
  padding-left: 0.95rem !important;
}

.admin-layout .el-menu-item.is-active-admin .icon,
.admin-layout .el-sub-menu.is-opened .icon,
.admin-layout .el-sub-menu:hover .icon,
.admin-layout .el-sub-menu:focus .icon,
.admin-layout .el-menu-item:hover .icon,
.admin-layout .el-menu-item:focus .icon {
  filter: invert(44%) sepia(86%) saturate(552%) hue-rotate(169deg) brightness(83%) contrast(97%);
}

.admin-layout .el-sub-menu .el-menu-item {
  color: black;
}

.admin-layout .el-sub-menu.is-active-admin .el-menu-item.is-active-admin {
  color: #0499de !important;
}

.admin-layout .el-menu--popup-container .el-menu-item.is-active-admin {
  color: var(--tw-pink-dark-color);
}

.admin-layout .el-submenu .el-menu-item {
  padding-right: 1.25rem;
}

.admin-layout .el-menu-item:hover,
.admin-layout .el-sub-menu.is-active-admin.is-opened .el-menu-item.is-active-admin {
  cursor: pointer;
}

.admin-layout .el-sub-menu.is-opened,
.admin-layout .el-sub-menu.is-opened .submenu-wrapper,
.admin-layout .el-sub-menu.is-opened ul {
  color: var(--tw-blue-500);
  background: #ffffff;
}

.admin-layout .el-sub-menu.is-opened .submenu-wrapper {
  margin-bottom: 5px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.admin-layout .el-sub-menu.is-opened {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.admin-layout .el-sub-menu.is-opened .el-sub-menu__title {
  color: var(--tw-blue-500);
}

.admin-layout .el-menu-item:focus {
  cursor: pointer;
}

.admin-layout .el-menu--collapse .menu-item-text {
  display: none;
}

.admin-layout .el-menu--collapse .el-menu-item {
  padding: 0 !important;
  justify-content: center;
}

.admin-layout .el-menu--collapse .el-sub-menu.menu-item-custom {
  justify-content: center;
}

/* .admin-layout .collapse-is-close .el-sub-menu.is-active-admin .el-sub-menu__title {
  padding-left: 8px;
} */

.admin-layout .collapse-is-close .el-sub-menu__title:hover {
  background: none;
  color: var(--tw-pink-dark-color);
}

.admin-layout .el-image__wrapper {
  position: relative;
}

.admin-layout .el-menu-item-group__title {
  padding: 0 !important;
}

.admin-layout .header-custom {
  position: absolute;
  right: 0;
  top: 10px;
}

.admin-layout .el-menu--collapse .el-sub-menu.is-active-admin {
  background: var(--tw-blue-300);
  border-radius: 8px;
  background-color: white;
}
.admin-layout .el-sub-menu.is-active-admin .icon,
.admin-layout .el-menu--collapse .el-sub-menu.is-active-admin .icon {
  filter: invert(44%) sepia(86%) saturate(552%) hue-rotate(169deg) brightness(83%) contrast(97%);
}

.admin-layout .admin-sidebar .el-sub-menu.is-active-admin,
.admin-layout .el-sub-menu.is-active-admin .submenu-wrapper {
  background-color: var(--main-background-sidebar-active) !important;
}

.admin-layout .admin-sidebar .submenu-inner {
  padding-bottom: 8px;
}

.admin-layout .admin-sidebar .el-sub-menu .el-menu-item {
  display: flex;
  padding: 20px 0 20px 10px !important;
  margin-left: 5px;
  margin-right: 8px;
}
.admin-layout .admin-sidebar > ul > .el-sub-menu:not(.is-active-admin) .el-menu-item {
  margin-left: 20px !important;
  padding-left: 10px !important;
}

.admin-layout .admin-sidebar .el-sub-menu .is-active-admin {
  border-radius: 4px;
}

.admin-layout .admin-sidebar .el-sub-menu.is-active-admin {
  border-radius: unset !important;
}

.admin-layout .el-sub-menu.is-active-admin .el-sub-menu__title {
  padding-left: 15px;
}

.admin-layout .admin-sidebar > ul > .el-menu-item.is-active-admin,
.admin-layout .el-sub-menu.is-active-admin {
  border-left: 3px solid rgb(255 255 255 / 45%);
}

.admin-layout .el-sub-menu.is-opened ul {
  background-color: var(--main-background-sidebar) !important;
}

#el-main {
  background-color: #e9e9e9;
  transition: margin-left.3s ease-in;
}

#el-main.collapse-is-close {
  margin-left: 45px;
}

.el-menu-item {
  display: flex;
  align-items: center;
  height: var(--el-menu-item-height);
  line-height: var(--el-menu-item-height);
  font-size: var(--el-menu-item-font-size);
  color: var(--el-menu-text-color);
  padding: 0 var(--el-menu-base-level-padding);
  list-style: none;
  cursor: pointer;
  position: relative;
  transition:
    border-color var(--el-transition-duration),
    background-color var(--el-transition-duration),
    color var(--el-transition-duration);
  box-sizing: border-box;
  white-space: nowrap;
}

.wrap-result .file-name {
  white-space: unset;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

#table-result-scroll {
  height: calc(100vh - 350px) !important;
  overflow: auto;
}

#table-result-upload th {
  text-align: left;
  padding: 5px 0 5px 10px;
}
#table-result-upload tr td {
  padding: 5px 0 5px 10px;
}
#table-result-upload tbody tr {
  background: #6ebd952b !important;
}

.file-name-result {
  word-break: break-all;
  white-space: unset;
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.outer {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 400px);
  border-radius: 5px;
}

.block-1 {
  background-color: #fff;
  height: 100%;
  border-radius: 5px 0 0 5px;
}

.block-2 {
  background-color: #fff;
  flex: 1; /* adjust automatically */
  min-width: 0; /* allow flexing beyond auto width */
  overflow: hidden; /* hide overflow on small width */
  height: 100%;
}

.slider {
  line-height: 100%;
  width: 0.5rem;
  background-color: #ffffffb8;
  border: none;
  cursor: col-resize;
  user-select: none;
  text-align: center;
  height: 100%;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

.result-tab-active {
  border-bottom: 3px solid #333;
  color: #000000 !important;
  font-weight: 500;
}

.btn-upload {
  border-radius: 5px;
  padding: 20px;
  font-size: 16px;
}

.wrap-upload {
  border: 2px dotted #acaaaa;
  border-radius: 10px;
  color: #787878;
  cursor: pointer;
  height: 100%;
}

.wrap-upload:hover {
  color: #0082be;
  border-color: #0082be;
}

.list-file {
  border-radius: 5px;
}

.wrap-btn {
  margin-top: 10px;
  border-radius: 5px;
  padding: 10px;
}
