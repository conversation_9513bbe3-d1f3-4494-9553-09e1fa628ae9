<script setup lang="ts">
import LayoutBlank from '@layout/LayoutBlank.vue'
import LayoutDefault from '@layout/LayoutDefault.vue'
import { markRaw, shallowRef, watch } from 'vue'
import { useRoute } from 'vue-router'

const layout = shallowRef(markRaw(LayoutDefault))
const route = useRoute()

watch(
  () => route.meta,
  async (meta) => {
    try {
      if (!meta.layout) {
        layout.value = route.name ? LayoutDefault : LayoutBlank

        return
      }

      const component = await import(`@layout/${meta.layout}.vue`)
      layout.value = component?.default || LayoutDefault
    } catch (e) {
      layout.value = LayoutDefault
    }
  },
  { immediate: true }
)
</script>

<template>
  <component :is="layout">
    <slot></slot>
  </component>
</template>

<style>
#app {
  padding: 0 !important;
}
</style>
