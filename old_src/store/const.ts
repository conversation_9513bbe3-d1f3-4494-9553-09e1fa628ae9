interface ILabel {
  id: number
  label: string
  name: string
  active: boolean
}
export interface IImageList {
  file: string
  filePath: string
  fileName: string
}

export const loadingSvg = `
<path class="path" d="
  M 30 15
  L 28 17
  M 25.61 25.61
  A 15 15, 0, 0, 1, 15 30
  A 15 15, 0, 1, 1, 27.99 7.5
  L 15 15
" style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
`

export function resetSelectProblems(problems: ILabel[]) {
  return problems.map((item: ILabel) => (item.active = false))
}

export const TABLE_LANGUAGE = [
  {
    id: 1,
    label: 'Vietnamese',
    name: 'vi',
    active: false
  },
  {
    id: 2,
    label: 'English',
    name: 'en',
    active: false
  },
  {
    id: 3,
    label: 'French',
    name: 'fr',
    active: false
  },
  {
    id: 4,
    label: 'Japanese',
    name: 'ja',
    active: false
  }
]

export const PAPER_PROBLEMS = [
  {
    id: 1,
    label: 'ID Card',
    name: 'ID_CARD',
    active: false
  },
  {
    id: 2,
    label: 'Invoice',
    name: 'INVOICE',
    active: false
  },
  {
    id: 3,
    label: 'Receipt',
    name: 'RECEIPT',
    active: false
  },
  {
    id: 4,
    label: 'Card',
    name: 'CARD',
    active: false
  }
]

export const SUB_PROBLEMS_ID_CARD_VN = [
  {
    id: 1,
    label: 'Chip Identity',
    name: 'CHIP_ID',
    active: false
  },
  {
    id: 2,
    label: '12-Digit Identity Card',
    name: 'CMND_12',
    active: false
  },
  {
    id: 3,
    label: '9-Digit Identity Card',
    name: 'CMND_9',
    active: false
  },
  {
    id: 4,
    label: 'Passport',
    name: 'PASSPORT',
    active: false
  },
  {
    id: 5,
    label: 'Driver License',
    name: 'LICENSE',
    active: false
  }
]

export const SUB_PROBLEMS_INVOICE_EXTRACT = [
  {
    id: 1,
    label: 'Invoice',
    name: 'INVOICE',
    active: false
  }
]

export const SUB_PROBLEMS_RECEIPT_EXTRACT = [
  {
    id: 1,
    label: 'Receipt',
    name: 'RECEIPT',
    active: false
  }
]

export const SUB_PROBLEMS_CARD_EXTRACT = [
  {
    id: 1,
    label: 'Card Visit',
    name: 'CARD_VISIT',
    active: false
  },
]

export const SUB_PROBLEMS_ID_CARD_EN = [
  {
    id: 1,
    label: 'Identity Card',
    name: 'ID_CARD',
    active: false
  },
  {
    id: 2,
    label: 'Passport',
    name: 'PASSPORT',
    active: false
  },
  {
    id: 3,
    label: 'Driver License',
    name: 'LICENSE',
    active: false
  }
]
export const SUB_PROBLEMS_ID_CARD_FR = [
  {
    id: 1,
    label: 'Identity Card',
    name: 'ID_CARD',
    active: false
  },
  {
    id: 2,
    label: 'Passport',
    name: 'PASSPORT',
    active: false
  },
  {
    id: 3,
    label: 'Driver License',
    name: 'LICENSE',
    active: false
  }
]

export const SUB_PROBLEMS_ID_CARD_JP = [
  {
    id: 1,
    label: 'Identity Card',
    name: 'ID_CARD',
    active: false
  },
  {
    id: 2,
    label: 'Passport',
    name: 'PASSPORT',
    active: false
  },
  {
    id: 3,
    label: 'Driver License',
    name: 'LICENSE',
    active: false
  }
]

export const VIETNAM_CERTIFICATE_CONVERT_TO_FRENCH: Record<string, string> = {
  docType: 'Document Type',
  birthdate: 'Date of birth',
  issuedDate: 'Issued date',
  lastname: 'Name',
  firstname: 'First name',
  sex: 'Sex',
  id: 'ID',
  fullname: 'Full name',
  birth: 'Date of birth',
  origin: 'Origin',
  residence: 'Residence',
  ethnic: 'Ethnic',
  religion: 'Religion',
  features: 'Features',
  issue_date: 'Issue date',
  issue_place: 'Issue place'
}

export const IMAGE_OCR_PROBLEMS = [
  {
    id: 1,
    name: 'GENERAL',
    active: false
  }
]

export const EXPORT_CARD_ID_VN = [
  {
    file: '/images/9-id-card-vn/vn-9-1.png',
    filePath: 'images/9-id-card-vn/vn-9-1.png',
    fileName: 'vn-9-1.png'
  },
  {
    file: '/images/9-id-card-vn/vn-9-2.png',
    filePath: 'images/9-id-card-vn/vn-9-2.png',
    fileName: 'vn-9-2.png'
  }
]
export const EXPORT_CARD_ID_12_VN = [
  {
    file: '/images/12-id-card-vn/vn-12-1.jpg',
    filePath: 'images/12-id-card-vn/vn-12-1.jpg',
    fileName: 'vn-12-1.jpg'
  },
  {
    file: '/images/12-id-card-vn/vn-12-2.png',
    filePath: 'images/12-id-card-vn/vn-12-2.png',
    fileName: 'vn-12-2.png'
  },
  {
    file: '/images/12-id-card-vn/vn-12-3.png',
    filePath: 'images/12-id-card-vn/vn-12-3.png',
    fileName: 'vn-12-3.png'
  },
  {
    file: '/images/12-id-card-vn/vn-12-4.png',
    filePath: 'images/12-id-card-vn/vn-12-4.png',
    fileName: 'vn-12-4.png'
  }
]
export const EXPORT_CARD_ID_CHIP_VN = [
  {
    file: '/images/chip-id-vn/vn-chip-1.jpeg',
    filePath: 'images/chip-id-vn/vn-chip-1.jpeg',
    fileName: 'vn-chip-1.jpeg'
  },
  {
    file: '/images/chip-id-vn/vn-chip-2.jpeg',
    filePath: 'images/chip-id-vn/vn-chip-2.jpeg',
    fileName: 'vn-chip-2.jpeg'
  },
  {
    file: '/images/chip-id-vn/vn-chip-3.jpeg',
    filePath: 'images/chip-id-vn/vn-chip-3.jpeg',
    fileName: 'vn-chip-3.jpeg'
  }
]
export const EXPORT_CARD_PASSPORT_VN = [
  {
    file: '/images/passport-vn/vn-passport-1.jpg',
    filePath: 'images/passport-vn/vn-passport-1.jpg',
    fileName: 'vn-passport-1.jpg'
  },
  {
    file: '/images/passport-vn/vn-passport-2.jpg',
    filePath: 'images/passport-vn/vn-passport-2.jpg',
    fileName: 'vn-passport-2.jpg'
  },
  {
    file: '/images/passport-vn/vn-passport-3.jpg',
    filePath: 'images/passport-vn/vn-passport-3.jpg',
    fileName: 'vn-passport-3.jpg'
  }
]
export const EXPORT_CARD_LICENSE_VN = [
  {
    file: '/images/license-vn/vn-license-1.png',
    filePath: 'images/license-vn/vn-license-1.png',
    fileName: 'vn-license-1.png'
  },
  {
    file: '/images/license-vn/vn-license-2.png',
    filePath: 'images/license-vn/vn-license-2.png',
    fileName: 'vn-license-2.png'
  }
]
export const EXPORT_CARD_CHIP_FR = [
  {
    file: '/images/id-card-fr/fr-id-card-1.jpeg',
    filePath: 'images/id-card-fr/fr-id-card-1.jpeg',
    fileName: 'fr-id-card-1.jpeg'
  },
  {
    file: '/images/id-card-fr/fr-id-card-2.jpeg',
    filePath: 'images/id-card-fr/fr-id-card-2.jpeg',
    fileName: 'fr-id-card-2.jpeg'
  },
]

export const EXPORT_CARD_PASSPORT_FR = [
  {
    file: '/images/passport-fr/2.png',
    filePath: 'images/passport-fr/2.png',
    fileName: '2.png'
  },
  {
    file: '/images/passport-fr/3.jpg',
    filePath: 'images/passport-fr/3.jpg',
    fileName: '3.jpg'
  }
]
export const EXPORT_CARD_LICENSE_FR = [
  {
    file: '/images/license-fr/fr-license-3.jpg',
    filePath: 'images/license-fr/fr-license-3.jpg',
    fileName: 'fr-license-3.jpg'
  },
  {
    file: '/images/license-fr/fr-license-1.jpg',
    filePath: 'images/license-fr/fr-license-1.jpg',
    fileName: 'fr-license-1.jpg'
  },
  {
    file: '/images/license-fr/fr-license-2.jpg',
    filePath: 'images/license-fr/fr-license-2.jpg',
    fileName: 'fr-license-2.jpg'
  }
]

export const EXPORT_CARD_CHIP_JP = [
  {
    file: '/images/id-card-jp/jp-id-card-2.jpg',
    filePath: 'images/id-card-jp/jp-id-card-2.jpg',
    fileName: 'jp-id-card-2.jpg'
  },
  {
    file: '/images/id-card-jp/jp-id-card-3.jpg',
    filePath: 'images/id-card-jp/jp-id-card-3.jpg',
    fileName: 'jp-id-card-3.jpg'
  }
]

export const EXPORT_CARD_PASSPORT_JP = [
  {
    file: '/images/passport-jp/1.jpg',
    filePath: 'images/passport-jp/1.jpg',
    fileName: '1.jpg'
  }
]
export const EXPORT_CARD_LICENSE_JP = [
  {
    file: '/images/license-jp/jp-license-3.jpg',
    filePath: 'images/license-jp/jp-license-3.jpg',
    fileName: 'jp-license-3.jpg'
  },
  {
    file: '/images/license-jp/jp-license-1.png',
    filePath: 'images/license-jp/jp-license-1.png',
    fileName: 'jp-license-1.png'
  },
  {
    file: '/images/license-jp/jp-license-2.jpeg',
    filePath: 'images/license-jp/jp-license-2.jpeg',
    fileName: 'jp-license-2.jpeg'
  }
]

export const EXPORT_CARD_CHIP_EN = [
  {
    file: '/images/id-card-en/1621241741356.jpeg',
    filePath: 'images/id-card-en/1621241741356.jpeg',
    fileName: '1621241741356.jpeg'
  },
  {
    file: '/images/id-card-en/idnyc_front.jpg',
    filePath: 'images/id-card-en/idnyc_front.jpg',
    fileName: 'idnyc_front.jpg'
  },
  {
    file: '/images/id-card-en/new-york-real-id-license.jpg',
    filePath: 'images/id-card-en/new-york-real-id-license.jpg',
    fileName: 'new-york-real-id-license.jpg'
  }
]

export const EXPORT_CARD_PASSPORT_EN = [
  {
    file: '/images/passport-en/en-passport-1.jpg',
    filePath: 'images/passport-en/en-passport-1.jpg',
    fileName: 'en-passport-1.jpg'
  },
  {
    file: '/images/passport-en/passport_card_2.jpeg',
    filePath: 'images/passport-en/passport_card_2.jpeg',
    fileName: 'passport_card_2.jpeg'
  },
  {
    file: '/images/passport-en/passport_card_3.jpg',
    filePath: 'images/passport-en/passport_card_3.jpg',
    fileName: 'passport_card_3.jpg'
  },
  {
    file: '/images/passport-en/passport_card_4.jpg',
    filePath: 'images/passport-en/passport_card_4.jpg',
    fileName: 'passport_card_4.jpg'
  },
  {
    file: '/images/passport-en/passport_card_5.jpg',
    filePath: 'images/passport-en/passport_card_5.jpg',
    fileName: 'Passport_card_5.jpg'
  },
  {
    file: '/images/passport-en/en-passport-6.jpeg',
    filePath: 'images/passport-en/en-passport-6.jpeg',
    fileName: 'en-passport-6.jpeg'
  },
  {
    file: '/images/passport-en/en-passport-7.jpg',
    filePath: 'images/passport-en/en-passport-7.jpg',
    fileName: 'en-passport_7.jpg'
  },
  {
    file: '/images/passport-en/en-passport-8.jpg',
    filePath: 'images/passport-en/en-passport-8.jpg',
    fileName: 'en-passport_8.jpg'
  },
  {
    file: '/images/passport-en/en-passport-9.jpg',
    filePath: 'images/passport-en/en-passport-9.jpg',
    fileName: 'en-passport_9.jpg'
  },
  {
    file: '/images/passport-en/en-passport-10.jpg',
    filePath: 'images/passport-en/en-passport-10.jpg',
    fileName: 'en-passport_10.jpg'
  },
  {
    file: '/images/passport-en/en-passport-11.jpg',
    filePath: 'images/passport-en/en-passport-11.jpg',
    fileName: 'en-passport_11.jpg'
  }
]
export const EXPORT_CARD_LICENSE_EN = [
  {
    file: '/images/license-en/en-license-1.jpg',
    filePath: 'images/license-en/en-license-1.jpg',
    fileName: 'en-license-1.jpg'
  },
  {
    file: '/images/license-en/en-license-2.jpg',
    filePath: 'images/license-en/en-license-2.jpg',
    fileName: 'en-license-2.jpg'
  },{
    file: '/images/license-en/en-license-3.jpg',
    filePath: 'images/license-en/en-license-3.jpg',
    fileName: 'en-license-3.jpg'
  },{
    file: '/images/license-en/en-license-4.jpg',
    filePath: 'images/license-en/en-license-4.jpg',
    fileName: 'en-license-4.jpg'
  },{
    file: '/images/license-en/en-license-5.jpg',
    filePath: 'images/license-en/en-license-5.jpg',
    fileName: 'en-license-5.jpg'
  },{
    file: '/images/license-en/en-license-6.jpg',
    filePath: 'images/license-en/en-license-6.jpg',
    fileName: 'en-license-6.jpg'
  },{
    file: '/images/license-en/en-license-7.jpg',
    filePath: 'images/license-en/en-license-7.jpg',
    fileName: 'en-license-7.jpg'
  },
]

export const INVOICE_JP = [
  {
    file: '/images/table-invoice-jp/1.png',
    filePath: 'images/table-invoice-jp/1.png',
    fileName: '1.png'
  },
  {
    file: '/images/table-invoice-jp/2.png',
    filePath: 'images/table-invoice-jp/2.png',
    fileName: '2.png'
  },
  {
    file: '/images/table-invoice-jp/3.png',
    filePath: 'images/table-invoice-jp/3.png',
    fileName: '3.png'
  },
  {
    file: '/images/table-invoice-jp/4.png',
    filePath: 'images/table-invoice-jp/4.png',
    fileName: '4.png'
  },
  {
    file: '/images/table-invoice-jp/5.png',
    filePath: 'images/table-invoice-jp/5.png',
    fileName: '5.png'
  },
]

export const INVOICE_VN = [
  {
    file: '/images/invoice-vn/1.png',
    filePath: 'images/invoice-vn/1.png',
    fileName: '1.png'
  },
  {
    file: '/images/invoice-vn/2.png',
    filePath: 'images/invoice-vn/2.png',
    fileName: '2.png'
  }
]

export const INVOICE_EN = [
  {
    file: '/images/invoice-en/1.jpg',
    filePath: 'images/invoice-en/1.jpg',
    fileName: '1.jpg'
  },
  {
    file: '/images/invoice-en/2.png',
    filePath: 'images/invoice-en/2.png',
    fileName: '2.png'
  },
  {
    file: '/images/invoice-en/3.jpg',
    filePath: 'images/invoice-en/3.jpg',
    fileName: '3.jpg'
  },
  {
    file: '/images/invoice-en/4.jpg',
    filePath: 'images/invoice-en/4.jpg',
    fileName: '4.jpg'
  },
  {
    file: '/images/invoice-en/5.jpg',
    filePath: 'images/invoice-en/5.jpg',
    fileName: '5.jpg'
  },
  {
    file: '/images/invoice-en/6.jpg',
    filePath: 'images/invoice-en/6.jpg',
    fileName: '6.jpg'
  },
  {
    file: '/images/invoice-en/7.jpg',
    filePath: 'images/invoice-en/7.jpg',
    fileName: '7.jpg'
  },
  {
    file: '/images/invoice-en/8.png',
    filePath: 'images/invoice-en/8.png',
    fileName: '8.png'
  },
  {
    file: '/images/invoice-en/9.png',
    filePath: 'images/invoice-en/9.png',
    fileName: '9.png'
  }
]

export const INVOICE_FR = [
  {
    file: '/images/invoice-fr/1.jpeg',
    filePath: 'images/invoice-fr/1.jpeg',
    fileName: '1.jpeg'
  }
]

export const RECEIPT_FR = [
  {
    file: '/images/receipt-fr/fr-receipt-1.png',
    filePath: 'images/receipt-fr/fr-receipt-1.png',
    fileName: 'fr-receipt-1.png'
  },
  {
    file: '/images/receipt-fr/fr-receipt-4.jpg',
    filePath: 'images/receipt-fr/fr-receipt-4.jpg',
    fileName: 'fr-receipt-4.jpg'
  },
  {
    file: '/images/receipt-fr/fr-receipt-5.jpg',
    filePath: 'images/receipt-fr/fr-receipt-5.jpg',
    fileName: 'fr-receipt-5.jpg'
  },
  {
    file: '/images/receipt-fr/fr-receipt-6.jpg',
    filePath: 'images/receipt-fr/fr-receipt-6.jpg',
    fileName: 'fr-receipt-6.jpg'
  },
  {
    file: '/images/receipt-fr/fr-receipt-7.jpg',
    filePath: 'images/receipt-fr/fr-receipt-7.jpg',
    fileName: 'fr-receipt-7.jpg'
  },
  {
    file: '/images/receipt-fr/fr-receipt-8.jpg',
    filePath: 'images/receipt-fr/fr-receipt-8.jpg',
    fileName: 'fr-receipt-8.jpg'
  },
  {
    file: '/images/receipt-fr/fr-receipt-9.png',
    filePath: 'images/receipt-fr/fr-receipt-9.png',
    fileName: 'fr-receipt-9.png'
  },
  {
    file: '/images/receipt-fr/fr-receipt-10.png',
    filePath: 'images/receipt-fr/fr-receipt-10.png',
    fileName: 'fr-receipt-10.png'
  },
  {
    file: '/images/receipt-fr/fr-receipt-11.png',
    filePath: 'images/receipt-fr/fr-receipt-11.png',
    fileName: 'fr-receipt-11.png'
  },
  {
    file: '/images/receipt-fr/fr-receipt-12.jpg',
    filePath: 'images/receipt-fr/fr-receipt-12.jpg',
    fileName: 'fr-receipt-12.jpg'
  },
  {
    file: '/images/receipt-fr/2.jpg',
    filePath: 'images/receipt-fr/2.jpg',
    fileName: '2.jpg'
  },
  {
    file: '/images/receipt-fr/3.jpg',
    filePath: 'images/receipt-fr/3.jpg',
    fileName: '3.jpg'
  }
]

export const RECEIPT_VN = [
  {
    file: '/images/receipt-vn/vn-receipt-1.png',
    filePath: 'images/receipt-vn/vn-receipt-1.png',
    fileName: 'vn-receipt-1.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-2.jpeg',
    filePath: 'images/receipt-vn/vn-receipt-2.jpeg',
    fileName: 'vn-receipt-2.jpeg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-3.jpg',
    filePath: 'images/receipt-vn/vn-receipt-3.jpg',
    fileName: 'vn-receipt-3.jpg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-4.jpg',
    filePath: 'images/receipt-vn/vn-receipt-4.jpg',
    fileName: 'vn-receipt-4.jpg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-5.jpg',
    filePath: 'images/receipt-vn/vn-receipt-5.jpg',
    fileName: 'vn-receipt-5.jpg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-6.jpeg',
    filePath: 'images/receipt-vn/vn-receipt-6.jpeg',
    fileName: 'vn-receipt-6.jpeg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-7.jpeg',
    filePath: 'images/receipt-vn/vn-receipt-7.jpeg',
    fileName: 'vn-receipt-7.jpeg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-8.jpeg',
    filePath: 'images/receipt-vn/vn-receipt-8.jpeg',
    fileName: 'vn-receipt-8.jpeg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-9.jpeg',
    filePath: 'images/receipt-vn/vn-receipt-9.jpeg',
    fileName: 'vn-receipt-9.jpeg'
  },
  {
    file: '/images/receipt-vn/vn-receipt-10.png',
    filePath: 'images/receipt-vn/vn-receipt-10.png',
    fileName: 'vn-receipt-10.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-11.png',
    filePath: 'images/receipt-vn/vn-receipt-11.png',
    fileName: 'vn-receipt-11.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-12.png',
    filePath: 'images/receipt-vn/vn-receipt-12.png',
    fileName: 'vn-receipt-12.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-13.png',
    filePath: 'images/receipt-vn/vn-receipt-13.png',
    fileName: 'vn-receipt-13.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-14.png',
    filePath: 'images/receipt-vn/vn-receipt-14.png',
    fileName: 'vn-receipt-14.png'
  },
  {
    file: '/images/receipt-vn/vn-receipt-15.jpg',
    filePath: 'images/receipt-vn/vn-receipt-15.jpg',
    fileName: 'vn-receipt-15.jpg'
  },
]

export const RECEIPT_EN = [
  {
    file: '/images/receipt-en/1.jpg',
    filePath: 'images/receipt-en/1.jpg',
    fileName: '1.jpg'
  },
  {
    file: '/images/receipt-en/2.jpg',
    filePath: 'images/receipt-en/2.jpg',
    fileName: '2.jpg'
  },
  {
    file: '/images/receipt-en/3.jpg',
    filePath: 'images/receipt-en/3.jpg',
    fileName: '3.jpg'
  },
  {
    file: '/images/receipt-en/4.jpg',
    filePath: 'images/receipt-en/4.jpg',
    fileName: '4.jpg'
  },
  {
    file: '/images/receipt-en/5.jpg',
    filePath: 'images/receipt-en/5.jpg',
    fileName: '5.jpg'
  },
  {
    file: '/images/receipt-en/en-receipt-6.jpg',
    filePath: 'images/receipt-en/en-receipt-6.jpg',
    fileName: 'en-receipt-6.jpg'
  },
  {
    file: '/images/receipt-en/en-receipt-7.jpg',
    filePath: 'images/receipt-en/en-receipt-7.jpg',
    fileName: 'en-receipt-7.jpg'
  },
  {
    file: '/images/receipt-en/en-receipt-8.jpg',
    filePath: 'images/receipt-en/en-receipt-8.jpg',
    fileName: 'en-receipt-8.jpg'
  },
  {
    file: '/images/receipt-en/en-receipt-9.png',
    filePath: 'images/receipt-en/en-receipt-9.png',
    fileName: 'en-receipt-9.png'
  },
  {
    file: '/images/receipt-en/en-receipt-10.png',
    filePath: 'images/receipt-en/en-receipt-10.png',
    fileName: 'en-receipt-10.png'
  },
  {
    file: '/images/receipt-en/en-receipt-11.png',
    filePath: 'images/receipt-en/en-receipt-11.png',
    fileName: 'en-receipt-11.png'
  },
  {
    file: '/images/receipt-en/en-receipt-12.png',
    filePath: 'images/receipt-en/en-receipt-12.png',
    fileName: 'en-receipt-12.png'
  },
  {
    file: '/images/receipt-en/en-receipt-13.png',
    filePath: 'images/receipt-en/en-receipt-13.png',
    fileName: 'en-receipt-13.png'
  },
  {
    file: '/images/receipt-en/en-receipt-14.jpg',
    filePath: 'images/receipt-en/en-receipt-14.jpg',
    fileName: 'en-receipt-14.jpg'
  },
  {
    file: '/images/receipt-en/en-receipt-15.png',
    filePath: 'images/receipt-en/en-receipt-15.png',
    fileName: 'en-receipt-15.png'
  },
  {
    file: '/images/receipt-en/en-receipt-16.png',
    filePath: 'images/receipt-en/en-receipt-16.png',
    fileName: 'en-receipt-16.png'
  },
  {
    file: '/images/receipt-en/en-receipt-17.png',
    filePath: 'images/receipt-en/en-receipt-17.png',
    fileName: 'en-receipt-17.png'
  },
]

export const RECEIPT_JP = [
  {
    file: '/images/receipt-jp/ja-receipt-1.png',
    filePath: 'images/receipt-jp/ja-receipt-1.png',
    fileName: 'ja-receipt-1.png'
  },
  {
    file: '/images/receipt-jp/ja-receipt-2.png',
    filePath: 'images/receipt-jp/ja-receipt-2.png',
    fileName: 'ja-receipt-2.png'
  },
  {
    file: '/images/receipt-jp/ja-receipt-3.jpeg',
    filePath: 'images/receipt-jp/ja-receipt-3.jpeg',
    fileName: 'ja-receipt-3.jpeg'
  },
  {
    file: '/images/receipt-jp/ja-receipt-4.jpg',
    filePath: 'images/receipt-jp/ja-receipt-4.jpg',
    fileName: 'ja-receipt-4.jpg'
  }
]

export const EXPORT_CARD_VISIT_EN = [
  {
    file: '/images/card-visit-en/card_visit_3.png',
    filePath: 'images/card-visit-en/card_visit_3.png',
    fileName: 'card_visit_3.png'
  },
  {
    file: '/images/card-visit-en/en-card-1.png',
    filePath: 'images/card-visit-en/en-card-1.png',
    fileName: 'en-card-1.png'
  },
  {
    file: '/images/card-visit-en/en-card-2.png',
    filePath: 'images/card-visit-en/en-card-2.png',
    fileName: 'en-card-2.png'
  },
  {
    file: '/images/card-visit-en/en-card-4.png',
    filePath: 'images/card-visit-en/en-card-4.png',
    fileName: 'en-card-4.png'
  },
  {
    file: '/images/card-visit-en/en-card-5.png',
    filePath: 'images/card-visit-en/en-card-5.png',
    fileName: 'en-card-5.png'
  }
]

export const EXPORT_CARD_VISIT_VN = [
  {
    file: '/images/card-visit-vn/vn-card-1.png',
    filePath: 'images/card-visit-vn/vn-card-1.png',
    fileName: 'vn-card-1.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-2.png',
    filePath: 'images/card-visit-vn/vn-card-2.png',
    fileName: 'vn-card-2.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-3.png',
    filePath: 'images/card-visit-vn/vn-card-3.png',
    fileName: 'vn-card-3.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-4.png',
    filePath: 'images/card-visit-vn/vn-card-4.png',
    fileName: 'vn-card-4.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-5.png',
    filePath: 'images/card-visit-vn/vn-card-5.png',
    fileName: 'vn-card-5.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-6.png',
    filePath: 'images/card-visit-vn/vn-card-6.png',
    fileName: 'vn-card-6.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-7.png',
    filePath: 'images/card-visit-vn/vn-card-7.png',
    fileName: 'vn-card-7.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-8.png',
    filePath: 'images/card-visit-vn/vn-card-8.png',
    fileName: 'vn-card-8.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-9.png',
    filePath: 'images/card-visit-vn/vn-card-9.png',
    fileName: 'vn-card-9.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-10.png',
    filePath: 'images/card-visit-vn/vn-card-10.png',
    fileName: 'vn-card-10.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-11.png',
    filePath: 'images/card-visit-vn/vn-card-11.png',
    fileName: 'vn-card-11.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-12.png',
    filePath: 'images/card-visit-vn/vn-card-12.png',
    fileName: 'vn-card-12.png'
  },
  {
    file: '/images/card-visit-vn/vn-card-13.png',
    filePath: 'images/card-visit-vn/vn-card-13.png',
    fileName: 'vn-card-13.png'
  },
]

export const EXPORT_CARD_VISIT_JP = [
  {
    file: '/images/card-visit-jp/1.jpg',
    filePath: 'images/card-visit-jp/1.jpg',
    fileName: '1.jpg'
  },
  {
    file: '/images/card-visit-jp/2.jpg',
    filePath: 'images/card-visit-jp/2.jpg',
    fileName: '2.jpg'
  },
  {
    file: '/images/card-visit-jp/3.jpg',
    filePath: 'images/card-visit-jp/3.jpg',
    fileName: '3.jpg'
  }
]

export const EXPORT_CARD_VISIT_FR = [
  {
    file: '/images/card-visit-fr/fr-card-1.png',
    filePath: 'images/card-visit-fr/fr-card-1.png',
    fileName: 'fr-card-1.png'
  },
  {
    file: '/images/card-visit-fr/fr-card-2.jpg',
    filePath: 'images/card-visit-fr/fr-card-2.jpg',
    fileName: 'fr-card-2.jpg'
  },
  {
    file: '/images/card-visit-fr/fr-card-3.jpg',
    filePath: 'images/card-visit-fr/fr-card-3.jpg',
    fileName: 'fr-card-3.jpg'
  },
  {
    file: '/images/card-visit-fr/fr-card-4.jpg',
    filePath: 'images/card-visit-fr/fr-card-4.jpg',
    fileName: 'fr-card-4.jpg'
  },
  {
    file: '/images/card-visit-fr/fr-card-5.jpg',
    filePath: 'images/card-visit-fr/fr-card-5.jpg',
    fileName: 'fr-card-5.jpg'
  },
  {
    file: '/images/card-visit-fr/fr-card-6.jpg',
    filePath: 'images/card-visit-fr/fr-card-6.jpg',
    fileName: 'fr-card-6.jpg'
  },
]

export const IMAGES_TABLE_GENERAL_EN = [
  {
    file: '/images/table-general-en/1.png',
    filePath: 'images/table-general-en/1.png',
    fileName: '1.png'
  },
  {
    file: '/images/table-general-en/4.png',
    filePath: 'images/table-general-en/4.png',
    fileName: '4.png'
  },
  {
    file: '/images/table-general-en-2/8ce656df5b8881d1fd6cd63a613ce87e.jpg',
    filePath: 'images/table-general-en-2/8ce656df5b8881d1fd6cd63a613ce87e.jpg',
    fileName: '8ce656df5b8881d1fd6cd63a613ce87e.jpg'
  },
  {
    file: '/images/table-general-en-2/8OBBLMFC6A47JYR1666050584.png',
    filePath: 'images/table-general-en-2/8OBBLMFC6A47JYR1666050584.png',
    fileName: '8OBBLMFC6A47JYR1666050584.png'
  },
  {
    file: '/images/table-general-en-2/64be86eaa29fa71f24b01049_Image-1-04.jpg',
    filePath: 'images/table-general-en-2/64be86eaa29fa71f24b01049_Image-1-04.jpg',
    fileName: '64be86eaa29fa71f24b01049_Image-1-04.jpg'
  },
  {
    file: '/images/table-general-en-2/invoice-template-classic_invoice.png',
    filePath: 'images/table-general-en-2/invoice-template-classic_invoice.png',
    fileName: 'invoice-template-classic_invoice.png'
  },
  {
    file: '/images/table-general-en-2/invoice-template-modern.png',
    filePath: 'images/table-general-en-2/invoice-template-modern.png',
    fileName: 'invoice-template-modern.png'
  }
]
export const IMAGES_TABLE_GENERAL_JP = [
  {
    file: '/images/table-general-jp/3.png',
    filePath: 'images/table-general-jp/3.png',
    fileName: '3.png'
  }
]
export const IMAGES_TABLE_JP_INVOICE = [
  {
    file: '/images/table-invoice-jp/1.png',
    filePath: 'images/table-invoice-jp/1.png',
    fileName: '1.png'
  },
  {
    file: '/images/table-invoice-jp/2.png',
    filePath: 'images/table-invoice-jp/2.png',
    fileName: '2.png'
  },
  {
    file: '/images/table-invoice-jp/3.png',
    filePath: 'images/table-invoice-jp/3.png',
    fileName: '3.png'
  },
  {
    file: '/images/table-invoice-jp/4.png',
    filePath: 'images/table-invoice-jp/4.png',
    fileName: '4.png'
  }
]
export const IMAGE_GENERAL_VN = [
  {
    file: '/images/table-general-vn-2/20211220001-724x1024.jpg',
    filePath: 'images/table-general-vn-2/20211220001-724x1024.jpg',
    fileName: '20211220001-724x1024.jpg'
  },
  {
    file: '/images/table-general-vn-2/Điều chỉnh lịch thi kết thúc học phần học kỳ I năm học 2021-2022_001.jpg',
    filePath:
      'images/table-general-vn-2/Điều chỉnh lịch thi kết thúc học phần học kỳ I năm học 2021-2022_001.jpg',
    fileName: 'Điều chỉnh lịch thi kết thúc học phần học kỳ I năm học 2021-2022_001.jpg'
  },
  {
    file: '/images/table-general-vn-2/Quan-ly-vat-lieu-xay-dung.jpg',
    filePath: 'images/table-general-vn-2/Quan-ly-vat-lieu-xay-dung.jpg',
    fileName: 'Quan-ly-vat-lieu-xay-dung.jpg'
  },
  {
    file: '/images/table-general-vn-2/mau-hoa-dien-tu-theo-TT-78.png',
    filePath: 'images/table-general-vn-2/mau-hoa-dien-tu-theo-TT-78.png',
    fileName: 'mau-hoa-dien-tu-theo-TT-78.png'
  }
]
export const IMAGE_GENERAL_JP = [
  {
    file: '/images/table-general-jp/3.png',
    filePath: 'images/table-general-jp/3.png',
    fileName: '3.png'
  }
]
export const IMAGE_GENERAL_EN = [
  {
    file: '/images/table-general-en/1.png',
    filePath: 'images/table-general-en/1.png',
    fileName: '1.png'
  },
  {
    file: '/images/table-general-en/4.png',
    filePath: 'images/table-general-en/4.png',
    fileName: '4.png'
  }
]

const getVNImageList = (prob: string) => {
  if (prob == 'CMND_9') return EXPORT_CARD_ID_VN
  else if (prob == 'CMND_12') return EXPORT_CARD_ID_12_VN
  else if (prob == 'CHIP_ID') return EXPORT_CARD_ID_CHIP_VN
  else if (prob == 'PASSPORT') return EXPORT_CARD_PASSPORT_VN
  else if (prob == 'LICENSE') return EXPORT_CARD_LICENSE_VN
  else if (prob == 'INVOICE') return INVOICE_VN
  else if (prob == 'RECEIPT') return RECEIPT_VN
  else if (prob == 'CARD_VISIT') return EXPORT_CARD_VISIT_VN
}

const getENImageList = (prob: string) => {
  if (prob == 'ID_CARD') return EXPORT_CARD_CHIP_EN
  else if (prob == 'PASSPORT') return EXPORT_CARD_PASSPORT_EN
  else if (prob == 'LICENSE') return EXPORT_CARD_LICENSE_EN
  else if (prob == 'INVOICE') return INVOICE_EN
  else if (prob == 'RECEIPT') return RECEIPT_EN
  else if (prob == 'CARD_VISIT') return EXPORT_CARD_VISIT_EN
}

const getJPImageList = (prob: string) => {
  if (prob == 'ID_CARD') return EXPORT_CARD_CHIP_JP
  else if (prob == 'PASSPORT') return EXPORT_CARD_PASSPORT_JP
  else if (prob == 'LICENSE') return EXPORT_CARD_LICENSE_JP
  else if (prob == 'INVOICE') return INVOICE_JP
  else if (prob == 'RECEIPT') return RECEIPT_JP
  else if (prob == 'CARD_VISIT') return EXPORT_CARD_VISIT_JP
}

const getFRImageList = (prob: string) => {
  if (prob == 'ID_CARD') return EXPORT_CARD_CHIP_FR
  else if (prob == 'PASSPORT') return EXPORT_CARD_PASSPORT_FR
  else if (prob == 'LICENSE') return EXPORT_CARD_LICENSE_FR
  else if (prob == 'INVOICE') return INVOICE_FR
  else if (prob == 'RECEIPT') return RECEIPT_FR
  else if (prob == 'CARD_VISIT') return EXPORT_CARD_VISIT_FR
}

export const getImageList = (lang: string, prob: string) => {
  if (lang == 'vi') return getVNImageList(prob)
  else if (lang == 'en') return getENImageList(prob)
  else if (lang == 'ja') return getJPImageList(prob)
  else if (lang == 'fr') return getFRImageList(prob)
}
