import { i18n } from '@lib/i18n'
import { changeLocalSetting, getLocalSetting } from '@src/utils/setting'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export enum LocaleEnum {
  EN = 'en',
  VI = 'vi'
}

export const localeMap = {
  [LocaleEnum.EN]: {
    key: LocaleEnum.EN,
    flag: '🇬🇧',
    name: 'English',
    language: 'English'
  },
  [LocaleEnum.VI]: {
    key: LocaleEnum.VI,
    flag: '🇻🇳',
    name: 'Việt Nam',
    language: 'Tiếng Việt'
  }
}

export const DefaultLocale = LocaleEnum.EN

export const useLocaleStore = defineStore('locale', () => {
  const setting = getLocalSetting()

  const defaultLocale = ref(DefaultLocale)
  const currentLocale = ref(setting.currentLocale || DefaultLocale)

  function changeLocale(locale: LocaleEnum) {
    currentLocale.value = locale
    i18n.locale = locale

    changeLocalSetting({ currentLocale: locale })
  }

  return { currentLocale, defaultLocale, changeLocale }
})
