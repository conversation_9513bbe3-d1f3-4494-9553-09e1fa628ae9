import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export const useAuthStore = defineStore('auth', () => {
  // TODO: update
  const isAuthenticated = ref(false)

  const router = useRouter()

  function login() {
    isAuthenticated.value = true
    router.push({ name: 'statistic' })
  }

  function logout() {
    isAuthenticated.value = false
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    router.push({ name: 'login'})
  }

  return { isAuthenticated, login, logout }
})
