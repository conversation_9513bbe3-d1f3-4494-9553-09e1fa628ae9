import { ref, computed } from 'vue'
import type { 
  Conversation, 
  Message, 
  Document,
  ApiResponse,
  ApiError,
  SendMessageRequest,
  SendMessageResponse,
  CreateConversationRequest,
  CreateConversationResponse,
  UploadDocumentRequest,
  UploadDocumentResponse
} from '@/types'
import {
  getAllSessions,
  getSessionHistory,
  createSession,
  updateSessionName,
  deleteSession,
  chat,
} from '@/services/api'

// Interface for the API message format
interface IMessage {
  role: 'user' | 'assistant'
  content: string
}

// Create API response wrapper
const createApiResponse = <T>(data: T, success = true, message?: string): ApiResponse<T> => ({
  data,
  success,
  message,
  timestamp: new Date().toISOString()
});

// Create API error
const createApiError = (code: string, message: string, details?: any): ApiError => ({
  code,
  message,
  details,
  timestamp: new Date().toISOString()
});

// Helper function to convert old API message format to new format
const convertMessage = (msg: any, index: number): Message => ({
  id: `msg-${Date.now()}-${index}`,
  content: msg.content || msg.message || '',
  role: msg.role === 'user' ? 'user' : 'assistant',
  timestamp: msg.timestamp || new Date().toISOString()
});

// Helper function to convert session to conversation
const convertSessionToConversation = (session: any, messages: Message[] = []): Conversation => ({
  id: session.id || session.session_id,
  title: session.name || session.title || 'New Conversation',
  lastMessage: messages.length > 0 ? messages[messages.length - 1].content : '',
  timestamp: session.updated_at || session.created_at || new Date().toISOString(),
  documentCount: 0,
  messages,
  isActive: false
});

export function useApi() {
  // Reactive state
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Helper to handle API calls with loading and error states
  const handleApiCall = async <T>(apiCall: () => Promise<T>): Promise<T> => {
    loading.value = true
    error.value = null
    
    try {
      const result = await apiCall()
      return result
    } catch (err: any) {
      error.value = err.message || 'An error occurred'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get all conversations
  const getConversations = async (): Promise<ApiResponse<Conversation[]>> => {
    return handleApiCall(async () => {
      try {
        const response = await getAllSessions();
        const sessions = response.data.data || response.data || [];
        
        // Convert sessions to conversations
        const conversations: Conversation[] = sessions.map((session: any) => 
          convertSessionToConversation(session)
        );

        return createApiResponse(conversations, true, 'Conversations fetched successfully');
      } catch (error: any) {
        console.error('Error fetching conversations:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch conversations', error);
      }
    })
  }

  // Get conversation by ID
  const getConversation = async (id: string): Promise<ApiResponse<Conversation | null>> => {
    return handleApiCall(async () => {
      try {
        const [sessionResponse, historyResponse] = await Promise.all([
          getAllSessions(),
          getSessionHistory(id)
        ]);

        const sessions = sessionResponse.data.data || sessionResponse.data || [];
        const session = sessions.find((s: any) => s.id === id || s.session_id === id);
        
        if (!session) {
          return createApiResponse(null, false, 'Conversation not found');
        }

        const messagesData = historyResponse.data.data || historyResponse.data || [];
        const messages: Message[] = messagesData.map((msg: any, index: number) => 
          convertMessage(msg, index)
        );

        const conversation = convertSessionToConversation(session, messages);
        return createApiResponse(conversation, true, 'Conversation found');
      } catch (error: any) {
        console.error('Error fetching conversation:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch conversation', error);
      }
    })
  }

  // Create new conversation
  const createConversation = async (request: CreateConversationRequest): Promise<ApiResponse<CreateConversationResponse>> => {
    return handleApiCall(async () => {
      try {
        const response = await createSession();
        const sessionData = response.data.data || response.data;
        
        const messages: Message[] = [];
        
        // If there's an initial message, send it to the chat API
        if (request.initialMessage) {
          const userMessage: Message = {
            id: `${sessionData.id}-msg-1`,
            content: request.initialMessage,
            role: 'user',
            timestamp: new Date().toISOString()
          };
          messages.push(userMessage);

          try {
            // Send the initial message to get AI response
            const chatMessages: IMessage[] = [{ role: 'user', content: request.initialMessage }];
            await chat(sessionData.id, chatMessages, request.collection_name);
            
            // For streaming responses, we'll add a placeholder AI message
            const aiMessage: Message = {
              id: `${sessionData.id}-msg-2`,
              content: 'Thinking...',
              role: 'assistant',
              timestamp: new Date().toISOString()
            };
            messages.push(aiMessage);
          } catch (chatError) {
            console.error('Error sending initial message:', chatError);
          }
        }

        // Update session name if provided
        if (request.title && request.title !== 'New Conversation') {
          try {
            await updateSessionName(sessionData.id, request.title);
          } catch (nameError) {
            console.error('Error updating session name:', nameError);
          }
        }

        const newConversation: Conversation = {
          id: sessionData.id,
          title: request.title || 'New Conversation',
          lastMessage: request.initialMessage || '',
          timestamp: new Date().toISOString(),
          documentCount: 0,
          messages,
          isActive: true
        };

        return createApiResponse({ conversation: newConversation }, true, 'Conversation created successfully');
      } catch (error: any) {
        console.error('Error creating conversation:', error);
        throw createApiError('CREATE_ERROR', 'Failed to create conversation', error);
      }
    })
  }

  // Send message
  const sendMessage = async (request: SendMessageRequest): Promise<ApiResponse<SendMessageResponse>> => {
    return handleApiCall(async () => {
      try {
        const userMessage: Message = {
          id: `${request.conversationId}-${Date.now()}`,
          content: request.content,
          role: 'user',
          timestamp: new Date().toISOString()
        };

        // Get conversation history to build context
        let conversationHistory: Message[] = [];
        try {
          const historyResponse = await getSessionHistory(request.conversationId);
          const messagesData = historyResponse.data.data || historyResponse.data || [];
          conversationHistory = messagesData.map((msg: any, index: number) => ({
            id: `history-${index}`,
            role: msg.role === 'user' ? 'user' : 'assistant',
            content: msg.content || msg.message || '',
            timestamp: msg.timestamp || new Date().toISOString()
          }));
        } catch (historyError) {
          console.warn('Could not fetch conversation history:', historyError);
        }

        // Add the new user message to history
        conversationHistory.push({
          id: `${request.conversationId}-${Date.now()}`,
          role: 'user',
          content: request.content,
          timestamp: new Date().toISOString()
        });

        // Send to chat API
        const chatResponse = await chat(request.conversationId, conversationHistory, 'General');
        
        // For streaming responses, create a placeholder AI message
        const aiMessage: Message = {
          id: `${request.conversationId}-${Date.now() + 1}`,
          content: 'Thinking...',
          role: 'assistant',
          timestamp: new Date().toISOString(),
          isLoading: true
        };

        return createApiResponse({ 
          message: userMessage, 
          aiResponse: aiMessage,
          streamResponse: chatResponse
        }, true, 'Message sent successfully');
      } catch (error: any) {
        console.error('Error sending message:', error);
        throw createApiError('SEND_ERROR', 'Failed to send message', error);
      }
    })
  }

  // Upload documents
  const uploadDocuments = async (request: UploadDocumentRequest): Promise<ApiResponse<UploadDocumentResponse>> => {
    return handleApiCall(async () => {
      try {
        const uploadedDocuments: Document[] = [];

        // Upload each file using the ragUpload API
        for (const file of request.files) {
          try {
            const response = await ragUpload(file);
            
            uploadedDocuments.push({
              name: file.name,
              type: getDocumentType(file.name),
              size: file.size,
              uploadedAt: new Date().toISOString(),
              content: `Uploaded: ${file.name}`
            });
          } catch (uploadError) {
            console.error(`Error uploading file ${file.name}:`, uploadError);
          }
        }

        return createApiResponse({ documents: uploadedDocuments }, true, 'Documents uploaded successfully');
      } catch (error: any) {
        console.error('Error uploading documents:', error);
        throw createApiError('UPLOAD_ERROR', 'Failed to upload documents', error);
      }
    })
  }

  // Helper function to get document type
  const getDocumentType = (filename: string): 'pdf' | 'doc' | 'txt' | 'image' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf': return 'pdf';
      case 'doc':
      case 'docx': return 'doc';
      case 'txt': return 'txt';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif': return 'image';
      default: return 'pdf';
    }
  }

  // Search conversations
  const searchConversations = async (query: string): Promise<ApiResponse<Conversation[]>> => {
    return handleApiCall(async () => {
      try {
        // Get all sessions and filter them
        const response = await getAllSessions();
        const sessions = response.data.data || response.data || [];

        const filteredSessions = sessions.filter((session: any) =>
          (session.name || session.title || '').toLowerCase().includes(query.toLowerCase())
        );

        const conversations: Conversation[] = filteredSessions.map((session: any) =>
          convertSessionToConversation(session)
        );

        return createApiResponse(conversations, true, 'Search completed successfully');
      } catch (error: any) {
        console.error('Error searching conversations:', error);
        throw createApiError('SEARCH_ERROR', 'Failed to search conversations', error);
      }
    })
  }

  // Rename conversation
  const renameConversation = async (conversationId: string, newTitle: string): Promise<ApiResponse<{ conversation: Conversation }>> => {
    return handleApiCall(async () => {
      try {
        await updateSessionName(conversationId, newTitle);

        // Get the updated conversation
        const conversationResponse = await getConversation(conversationId);
        if (!conversationResponse.data) {
          throw createApiError('NOT_FOUND', 'Conversation not found');
        }

        const updatedConversation = conversationResponse.data;
        updatedConversation.title = newTitle;
        updatedConversation.timestamp = new Date().toISOString();

        return createApiResponse(
          { conversation: updatedConversation },
          true,
          'Conversation renamed successfully'
        );
      } catch (error: any) {
        console.error('Error renaming conversation:', error);
        throw createApiError('UPDATE_ERROR', 'Failed to rename conversation', error);
      }
    })
  }

  // Delete conversation
  const deleteConversation = async (conversationId: string): Promise<ApiResponse<{ success: boolean }>> => {
    return handleApiCall(async () => {
      try {
        await deleteSession(conversationId);

        return createApiResponse(
          { success: true },
          true,
          'Conversation deleted successfully'
        );
      } catch (error: any) {
        console.error('Error deleting conversation:', error);
        throw createApiError('DELETE_ERROR', 'Failed to delete conversation', error);
      }
    })
  }

  // Get document content (placeholder - can be extended)
  const getDocumentContent = async (documentName: string): Promise<ApiResponse<any>> => {
    return handleApiCall(async () => {
      try {
        // For now, return mock content since the old API doesn't have a specific document content endpoint
        const content = {
          pages: [{
            number: 1,
            content: `Content for ${documentName}`,
            highlights: []
          }]
        };

        return createApiResponse(content, true, 'Document content fetched successfully');
      } catch (error: any) {
        console.error('Error fetching document content:', error);
        throw createApiError('FETCH_ERROR', 'Failed to fetch document content', error);
      }
    })
  }

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Methods
    getConversations,
    getConversation,
    createConversation,
    sendMessage,
    uploadDocuments,
    searchConversations,
    renameConversation,
    deleteConversation,
    getDocumentContent,

    // Clear error
    clearError: () => { error.value = null }
  }
}
