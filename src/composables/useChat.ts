import { ref, computed, watch } from 'vue';
import type { Ref } from 'vue';
import type { Message, Conversation, ChatState } from '@/types';
import { useApi } from '@/composables/useApi';

// Global chat state
const chatState = ref<ChatState>({
  conversations: [],
  selectedConversationId: null,
  messages: [],
  isLoading: false,
  error: null,
  mode: 'conversation'
});

export function useChat() {
  // Initialize the API composable
  const api = useApi();

  // Computed properties
  const selectedConversation = computed(() =>
    chatState.value.conversations.find(c => c.id === chatState.value.selectedConversationId)
  );

  const currentMessages = computed(() =>
    selectedConversation.value?.messages || []
  );

  const conversationCount = computed(() =>
    chatState.value.conversations.length
  );

  // Actions
  const loadConversations = async () => {
    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      const response = await api.getConversations();
      chatState.value.conversations = response.data;

      // Don't automatically select a conversation - let the route/component decide
      // This allows for proper new conversation (clean slate) behavior
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to load conversations';
      console.error('Error loading conversations:', error);
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const selectConversation = async (conversationId: string) => {
    // Prevent selecting the same conversation or multiple simultaneous selections
    if (chatState.value.selectedConversationId === conversationId || chatState.value.isLoading) {
      return;
    }

    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      const response = await api.getConversation(conversationId);
      if (response.data) {
        chatState.value.selectedConversationId = conversationId;

        // Update conversation in state
        const index = chatState.value.conversations.findIndex(c => c.id === conversationId);
        if (index !== -1) {
          chatState.value.conversations[index] = response.data;
        }
      }
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to load conversation';
      console.error('Error selecting conversation:', error);
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const createConversation = async (title?: string, initialMessage?: string) => {
    // Prevent multiple simultaneous creation attempts
    if (chatState.value.isLoading) {
      console.log('Conversation creation already in progress, skipping...');
      return;
    }

    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Creating new conversation with title:', title, 'initialMessage:', initialMessage);

      const response = await api.createConversation({ title, initialMessage });
      const newConversation = response.data.conversation;

      // Add the new conversation to the local state instead of reloading all
      const existingIndex = chatState.value.conversations.findIndex(c => c.id === newConversation.id);
      if (existingIndex === -1) {
        chatState.value.conversations.unshift(newConversation);
      } else {
        // Update existing conversation if it already exists
        chatState.value.conversations[existingIndex] = newConversation;
      }

      // Select the new conversation
      chatState.value.selectedConversationId = newConversation.id;

      console.log('Successfully created conversation:', newConversation.id);
      return newConversation;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to create conversation';
      console.error('Error creating conversation:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const sendMessage = async (content: string, conversationId?: string) => {
    // Use provided conversationId or fall back to selected conversation
    const targetConversationId = conversationId || chatState.value.selectedConversationId;

    // If no conversation exists, create one automatically
    if (!targetConversationId) {
      console.log('No conversation found, creating new conversation...');
      const newConversation = await createConversation('New Chat', content);
      if (!newConversation) {
        throw new Error('Failed to create conversation');
      }
      // The createConversation function already sets the selectedConversationId
      // and the message will be sent as part of the conversation creation
      return;
    }

    // Prevent multiple simultaneous message sends
    if (chatState.value.isLoading) {
      console.log('Message send already in progress, skipping...');
      return;
    }

    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Sending message to conversation:', targetConversationId);

      const response = await api.sendMessage({
        conversationId: targetConversationId,
        content
      });

      // Update conversation with new messages
      const conversation = chatState.value.conversations.find(
        c => c.id === targetConversationId
      );

      if (conversation) {
        if (!conversation.messages) conversation.messages = [];

        // Only add messages if they don't already exist (prevent duplicates)
        const userMessageExists = conversation.messages.some(
          m => m.content === response.data.message.content && m.role === 'user'
        );

        if (!userMessageExists) {
          conversation.messages.push(response.data.message);

          if (response.data.aiResponse) {
            conversation.messages.push(response.data.aiResponse);
          }

          conversation.lastMessage = content;
          conversation.timestamp = 'Now';
        }
      }

      // Ensure the conversation is selected if it wasn't already
      if (chatState.value.selectedConversationId !== targetConversationId) {
        chatState.value.selectedConversationId = targetConversationId;
      }

      return response.data;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to send message';
      console.error('Error sending message:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const searchConversations = async (query: string) => {
    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;
      
      const response = await api.searchConversations(query);
      return response.data;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to search conversations';
      console.error('Error searching conversations:', error);
      return [];
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const renameConversation = async (conversationId: string, newTitle: string) => {
    if (!newTitle.trim()) {
      throw new Error('Conversation title cannot be empty');
    }

    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Renaming conversation:', conversationId, 'to:', newTitle);

      const response = await api.renameConversation(conversationId, newTitle.trim());

      // Update the conversation in local state
      const conversation = chatState.value.conversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.title = newTitle.trim();
        conversation.timestamp = 'Now';
      }

      console.log('Successfully renamed conversation:', conversationId);
      return response.data;
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to rename conversation';
      console.error('Error renaming conversation:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      chatState.value.isLoading = true;
      chatState.value.error = null;

      console.log('Deleting conversation:', conversationId);

      await api.deleteConversation(conversationId);

      // Remove the conversation from local state
      const index = chatState.value.conversations.findIndex(c => c.id === conversationId);
      if (index !== -1) {
        chatState.value.conversations.splice(index, 1);
      }

      // If the deleted conversation was selected, clear selection
      if (chatState.value.selectedConversationId === conversationId) {
        chatState.value.selectedConversationId = null;
      }

      console.log('Successfully deleted conversation:', conversationId);
    } catch (error: any) {
      chatState.value.error = error.message || 'Failed to delete conversation';
      console.error('Error deleting conversation:', error);
      throw error;
    } finally {
      chatState.value.isLoading = false;
    }
  };

  const setMode = (mode: 'conversation' | 'document') => {
    chatState.value.mode = mode;
  };

  const clearError = () => {
    chatState.value.error = null;
  };

  const clearLoadingState = () => {
    chatState.value.isLoading = false;
    chatState.value.error = null;
  };

  // Initialize on first use
  const initialize = async () => {
    if (chatState.value.conversations.length === 0) {
      await loadConversations();
    }
  };

  // Set selected conversation by ID (for route-based selection)
  const setSelectedConversationId = (conversationId: string | null) => {
    chatState.value.selectedConversationId = conversationId;
  };

  // Validate if a conversation exists in the loaded conversations
  const validateConversationExists = (conversationId: string): boolean => {
    return chatState.value.conversations.some(c => c.id === conversationId);
  };

  // Check if conversations are loaded
  const areConversationsLoaded = (): boolean => {
    return chatState.value.conversations.length > 0 || !chatState.value.isLoading;
  };

  return {
    // State
    chatState: computed(() => chatState.value),
    selectedConversation,
    currentMessages,
    conversationCount,
    isLoading: computed(() => chatState.value.isLoading),
    error: computed(() => chatState.value.error),
    mode: computed(() => chatState.value.mode),
    
    // Actions
    loadConversations,
    selectConversation,
    createConversation,
    sendMessage,
    renameConversation,
    deleteConversation,
    searchConversations,
    setMode,
    clearError,
    clearLoadingState,
    initialize,
    setSelectedConversationId,
    validateConversationExists,
    areConversationsLoaded
  };
}
