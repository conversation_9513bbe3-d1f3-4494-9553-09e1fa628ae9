<template>
  <div id="app" class="min-h-screen bg-background text-foreground">
    <!-- Global Loading Indicator -->
    <Transition
      name="fade"
      enter-active-class="transition-opacity duration-200"
      leave-active-class="transition-opacity duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isGlobalLoading"
        class="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div class="text-center">
          <Loader2 class="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p class="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    </Transition>

    <!-- Main Router View -->
    <RouterView v-slot="{ Component, route }">
      <Transition
        name="page"
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-200 ease-in"
        enter-from-class="opacity-0 translate-y-4"
        enter-to-class="opacity-100 translate-y-0"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-4"
        mode="out-in"
      >
        <component :is="Component" :key="route.path" />
      </Transition>
    </RouterView>

    <!-- Global Error Boundary -->
    <div
      v-if="globalError"
      class="fixed top-4 right-4 bg-destructive text-destructive-foreground p-4 rounded-lg shadow-lg max-w-md z-50"
    >
      <div class="flex items-center gap-2">
        <AlertCircle class="h-4 w-4 flex-shrink-0" />
        <div class="flex-1">
          <h4 class="font-medium">Application Error</h4>
          <p class="text-sm opacity-90">{{ globalError }}</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          class="h-6 w-6 p-0 hover:bg-destructive-foreground/20"
          @click="clearGlobalError"
        >
          <X class="h-3 w-3" />
        </Button>
      </div>
    </div>

    <!-- Toast Notifications -->
    <Toaster />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onErrorCaptured, provide } from 'vue';
import { RouterView } from 'vue-router';
import { QueryClient } from '@tanstack/vue-query';
import { Loader2, AlertCircle, X } from 'lucide-vue-next';
import Button from '@/components/ui/Button.vue';
import { Toaster } from 'vue-sonner';

// Global state
const isGlobalLoading = ref(false);
const globalError = ref<string | null>(null);

// Create and provide query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// Provide query client to child components
provide('queryClient', queryClient);

// Global error handling
onErrorCaptured((error: Error) => {
  console.error('Global error captured:', error);
  globalError.value = error.message || 'An unexpected error occurred';
  return false; // Prevent error from propagating
});

// Global loading state management
const setGlobalLoading = (loading: boolean) => {
  isGlobalLoading.value = loading;
};

const clearGlobalError = () => {
  globalError.value = null;
};

// Provide global methods to child components
provide('setGlobalLoading', setGlobalLoading);
provide('setGlobalError', (error: string) => {
  globalError.value = error;
});

// Initialize app
onMounted(() => {
  // Set up global error handlers
  window.addEventListener('error', (event) => {
    console.error('Global window error:', event.error);
    globalError.value = 'An unexpected error occurred';
  });

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    globalError.value = 'An unexpected error occurred';
  });

  // Initialize theme
  const savedTheme = localStorage.getItem('theme') || 'dark';
  document.documentElement.setAttribute('data-theme', savedTheme);
});
</script>

<style>
/* Global transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(16px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-16px);
}
</style>
