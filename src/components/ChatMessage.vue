<template>
  <div :class="messageContainerClass">
    <!-- User Avatar -->
    <Avatar
      :size="'sm'"
      :class="avatarClass"
    >
      <template #fallback>
        <User v-if="isUser" class="h-3 w-3" />
        <Bot v-else class="h-3 w-3" />
      </template>
    </Avatar>

    <!-- Message Content -->
    <div :class="messageContentClass">
      <!-- Sender Label -->
      <div class="mb-1">
        <span class="text-xs text-muted-foreground">
          {{ isUser ? 'You' : 'AI Assistant' }}
        </span>
      </div>

      <!-- Message Bubble -->
      <div :class="messageBubbleClass">
        <div
          v-if="isUser"
          class="whitespace-pre-wrap break-words"
        >
          {{ message.content }}
        </div>
        <div
          v-else
          class="markdown-content break-words"
          v-html="renderedContent"
        ></div>

        <!-- Reasoning Section -->
        <ReasoningMessage
          v-if="message.reasoning && !isUser"
          :id="message.reasoning.id"
          :reasoning-content="message.reasoning.content"
          :loading="message.reasoning.loading"
          :time-range="message.reasoning.timeRange"
        />
        
        <!-- Document References -->
        <div
          v-if="message.documents && message.documents.length > 0"
          class="mt-3 space-y-2"
        >
          <div class="text-xs text-muted-foreground font-medium">
            Referenced documents:
          </div>
          <div class="space-y-1">
            <el-card
              v-for="doc in message.documents"
              :key="doc.name"
              shadow="hover"
              class="document-card cursor-pointer"
              @click="handleDocumentClick(doc.name)"
            >
              <div class="flex items-center gap-2">
                <i class="ri-file-text-line text-primary"></i>
                <div class="flex-1 min-w-0">
                  <div class="text-xs font-medium truncate">{{ doc.name }}</div>
                  <div v-if="doc.pages" class="text-xs text-muted-foreground">
                    Pages {{ doc.pages }}
                  </div>
                </div>
                <i class="ri-external-link-line text-xs text-muted-foreground"></i>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <!-- Timestamp -->
      <div class="mt-1 text-xs text-muted-foreground">
        {{ formatTimestamp(message.timestamp) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { User, Bot } from 'lucide-vue-next';
import type { ChatMessageProps } from '@/types';
import { formatTimestamp } from '@/lib/utils';
import { renderMarkdown } from '@/utils/markdown';
import Avatar from '@/components/ui/Avatar.vue';
import ReasoningMessage from '@/components/ReasoningMessage.vue';

const props = defineProps<ChatMessageProps>();

const emit = defineEmits<{
  documentClick: [documentName: string];
}>();

const isUser = computed(() => props.message.sender === 'user');

const renderedContent = computed(() => {
  if (isUser.value) {
    return props.message.content;
  }
  return renderMarkdown(props.message.content);
});

const messageContainerClass = computed(() => [
  'flex gap-3 mb-4',
  isUser.value ? 'flex-row-reverse' : 'flex-row'
]);

const avatarClass = computed(() => [
  isUser.value ? 'bg-primary' : 'bg-muted',
  isUser.value ? 'text-primary-foreground' : 'text-muted-foreground'
]);

const messageContentClass = computed(() => [
  'flex-1 max-w-[70%]',
  isUser.value ? 'text-right' : 'text-left'
]);

const messageBubbleClass = computed(() => [
  'inline-block px-3 py-2 rounded-lg text-sm max-w-full',
  isUser.value 
    ? 'bg-muted border border-border' 
    : 'bg-background border border-border shadow-sm'
]);

const handleDocumentClick = (documentName: string) => {
  emit('documentClick', documentName);
};
</script>

<style scoped>
.document-card {
  transition: all 0.2s ease;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--background));
}

.document-card:hover {
  border-color: hsl(var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.15);
}

.document-card :deep(.el-card__body) {
  padding: 8px 12px;
}

/* Ensure markdown content inherits proper styling */
.markdown-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Override any conflicting styles for math content */
.markdown-content :deep(mjx-container) {
  display: inline-block;
  margin: 2px 0;
}

.markdown-content :deep(mjx-container[display="true"]) {
  display: block;
  text-align: center;
  margin: 1em 0;
}
</style>
