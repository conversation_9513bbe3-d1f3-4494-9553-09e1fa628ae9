<template>
  <div class="w-80 border-r border-border bg-background flex flex-col h-full">
    <!-- Header -->
    <div class="p-4 border-b border-border">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold">{{ t('sidebar.conversations') }}</h2>
        <div class="flex items-center gap-1">
          <LanguageSelector
            :show-label="false"
            :show-arrow="false"
            size="small"
            trigger-class="!border-0 !p-2"
          />
          <el-tooltip :content="currentTheme === 'dark' ? t('sidebar.theme.light') : t('sidebar.theme.dark')" placement="bottom">
            <el-button
              text
              circle
              size="small"
              @click="toggleTheme"
            >
              <i v-if="currentTheme === 'dark'" class="ri-sun-line"></i>
              <i v-else class="ri-moon-line"></i>
            </el-button>
          </el-tooltip>
          <el-tooltip :content="isSidebarCollapsed ? t('sidebar.collapse.show') : t('sidebar.collapse.hide')" placement="bottom">
            <el-button
              text
              circle
              size="small"
              @click="handleToggleSidebar"
            >
              <i v-if="isSidebarCollapsed" class="ri-sidebar-unfold-line"></i>
              <i v-else class="ri-sidebar-fold-line"></i>
            </el-button>
          </el-tooltip>
        </div>
      </div>

      <!-- Mode Tabs -->
      <div class="pt-2 pb-3 border-border">
        <el-segmented
          v-model="currentMode"
          :options="modeOptions"
          @change="handleModeChange"
          class="mode-tabs"
          block
        />
      </div>
      
      <!-- New Conversation Button -->
      <el-button
        type="primary"
        class="w-full"
        @click="handleNewConversation"
      >
        <i class="ri-add-line"></i>
        {{ t('navigation.new_conversation') }}
      </el-button>

      <!-- Search -->
      <div class="pt-2 w-full">
        <el-input
          v-model="searchQuery"
          :placeholder="t('sidebar.search_placeholder')"
          :prefix-icon="SearchIcon"
          clearable
          class="search-input"
        />
      </div>
    </div>

    <!-- Conversations List -->
    <ScrollArea class="flex-1">
      <div class="p-2">
        <div
          v-if="isLoading"
          class="flex items-center justify-center py-8"
        >
          <el-icon class="is-loading text-2xl text-primary">
            <i class="ri-loader-4-line"></i>
          </el-icon>
        </div>

        <el-empty
          v-else-if="error"
          description=""
          class="py-4"
        >
          <template #image>
            <i class="ri-error-warning-line text-4xl text-destructive"></i>
          </template>
          <template #description>
            <p class="text-sm text-destructive mb-2">{{ error }}</p>
          </template>
          <el-button
            type="primary"
            size="small"
            @click="loadConversations"
          >
            Try Again
          </el-button>
        </el-empty>

        <el-empty
          v-else-if="filteredConversations.length === 0"
          :description="searchQuery ? t('sidebar.no_search_results') : t('sidebar.no_conversations')"
          class="py-4"
        >
          <template #image>
            <i class="ri-message-3-line text-4xl text-muted-foreground"></i>
          </template>
          <el-button
            v-if="!searchQuery"
            type="primary"
            size="small"
            :loading="isLoading"
            :disabled="isLoading"
            @click="handleNewConversation"
          >
            Start a conversation
          </el-button>
        </el-empty>

        <div v-else class="space-y-1">
          <div
            v-for="conversation in filteredConversations"
            :key="conversation.id"
            :class="getConversationItemClass(conversation.id)"
            @click="handleSelectConversation(conversation.id)"
          >
            <div
              class="flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-colors"
              :class="{ 'pointer-events-none opacity-50': isLoading }"
            >

              <!-- Conversation Content -->         
              <div class="flex-1 min-w-0 pl-2">
                <div class="flex items-center justify-between mb-1">
                  <!-- Inline Editable Title -->
                  <div class="flex-1 min-w-0 mr-2">
                    <input
                      v-if="isRenaming && renamingConversationId === conversation.id"
                      v-model="renameValue"
                      ref="renameInput"
                      class="rename-input"
                      @blur="confirmRename"
                      @keyup.enter="confirmRename"
                      @keyup.esc="cancelRename"
                      @click.stop
                    />
                    <h3
                      v-else
                      class="text-sm font-medium truncate cursor-pointer hover:text-primary transition-colors"
                      :title="conversation.title"
                    >
                      {{ conversation.title }}
                    </h3>
                  </div>
                </div>

                <!-- Conversation Meta -->
                <div class="flex items-center gap-3 text-xs text-muted-foreground">
                  <div class="flex items-center gap-1">
                    <i class="ri-message-3-line"></i>
                    <span>{{ conversation.messages?.length || 0 }}</span>
                  </div>

                  <div v-if="conversation.documentCount" class="flex items-center gap-1">
                    <FileText class="h-3 w-3" />
                    <span>{{ conversation.documentCount }}</span>
                  </div>
                </div>
              </div>

              <!-- More Options -->
              <el-button
                text
                circle
                size="small"
                class="opacity-0 group-hover:opacity-100 transition-opacity"
                :disabled="isLoading"
                @click.stop="openConversationMenu(conversation.id, $event)"
              >
                <i class="ri-more-line"></i>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>

    <!-- Context Menu -->
    <div
      v-if="contextMenuVisible"
      class="context-menu-overlay"
      @click="closeContextMenu"
    >
      <div
        class="context-menu"
        :style="{
          left: contextMenuPosition.x + 'px',
          top: contextMenuPosition.y + 'px'
        }"
        @click.stop
      >
        <div
          class="context-menu-item"
          @click="startInlineRename(contextMenuConversationId!)"
        >
          <el-icon class="context-menu-icon">
            <EditIcon />
          </el-icon>
          <span>Rename</span>
        </div>
        <div
          class="context-menu-item context-menu-item--danger"
          @click="handleDeleteConversation(contextMenuConversationId!)"
        >
          <el-icon class="context-menu-icon">
            <DeleteIcon />
          </el-icon>
          <span>Delete</span>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import {
  Search as SearchIcon,
  Plus as PlusIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@element-plus/icons-vue';
import { FileText } from 'lucide-vue-next';
import type { ChatSidebarProps } from '@/types';
import { useChat } from '@/composables/useChat';
import { useUI } from '@/composables/useUI';
import { useI18n } from '@/composables/useI18n';
import { formatTimestamp, debounce } from '@/lib/utils';
import ScrollArea from '@/components/ui/ScrollArea.vue';
import LanguageSelector from '@/components/LanguageSelector.vue';
import { ElMessageBox, ElMessage } from 'element-plus';

const props = withDefaults(defineProps<ChatSidebarProps>(), {
  mode: 'conversation'
});

const emit = defineEmits<{
  selectConversation: [id: string];
  newConversation: [];
  modeChange: [mode: 'conversation' | 'document'];
  toggleSidebar: [];
}>();

const {
  chatState,
  loadConversations,
  searchConversations,
  renameConversation,
  deleteConversation,
  isLoading,
  error
} = useChat();

const router = useRouter();
const { currentTheme, toggleTheme } = useUI();
const { t } = useI18n();

const searchQuery = ref('');
const searchResults = ref<any[]>([]);
const currentMode = ref(props.mode);

// Context menu state
const contextMenuVisible = ref(false);
const contextMenuConversationId = ref<string | null>(null);
const contextMenuPosition = ref({ x: 0, y: 0 });

// Rename state
const isRenaming = ref(false);
const renamingConversationId = ref<string | null>(null);
const renameValue = ref('');

const modeOptions = computed(() => [
  { label: t('sidebar.mode.chat'), value: 'conversation' },
  { label: t('sidebar.mode.documents'), value: 'document' }
]);

const filteredConversations = computed(() => {
  if (searchQuery.value.trim()) {
    return searchResults.value;
  }
  return chatState.value.conversations;
});

const getConversationItemClass = (conversationId: string) => [
  'group hover:bg-accent/50 transition-colors',
  props.selectedConversation === conversationId && 'bg-accent'
];

const handleSelectConversation = (id: string) => {
  // Navigate to the conversation route
  router.push(`/chat/${id}`);
  console.log(filteredConversations.value);
  emit('selectConversation', id);
};

const handleNewConversation = () => {
  // Navigate to /chat without ID for new conversation
  router.push('/chat');
  emit('newConversation');
};

const handleModeChange = (mode: 'conversation' | 'document') => {
  currentMode.value = mode;
  emit('modeChange', mode);
};

const handleToggleSidebar = () => {
  emit('toggleSidebar');
};

const openConversationMenu = (conversationId: string, event: MouseEvent) => {
  event.stopPropagation();
  contextMenuConversationId.value = conversationId;
  contextMenuPosition.value = { x: event.clientX, y: event.clientY };
  contextMenuVisible.value = true;
};

const closeContextMenu = () => {
  contextMenuVisible.value = false;
  contextMenuConversationId.value = null;
};

const startInlineRename = (conversationId: string) => {
  const conversation = chatState.value.conversations.find(c => c.id === conversationId);
  if (conversation) {
    closeContextMenu(); // Close context menu if open
    isRenaming.value = true;
    renamingConversationId.value = conversationId;
    renameValue.value = conversation.title;

    // Focus the input after Vue updates the DOM
    nextTick(() => {
      const input = document.querySelector('.rename-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    });
  }
};

const cancelRename = () => {
  isRenaming.value = false;
  renamingConversationId.value = null;
  renameValue.value = '';
};

const confirmRename = async () => {
  if (!renamingConversationId.value || !renameValue.value.trim()) {
    cancelRename();
    return;
  }

  try {
    await renameConversation(renamingConversationId.value, renameValue.value.trim());
    cancelRename();
  } catch (error: any) {
    ElMessage.error(error.message || 'Failed to rename conversation');
    // Reset to original value on error
    const conversation = chatState.value.conversations.find(c => c.id === renamingConversationId.value);
    if (conversation) {
      renameValue.value = conversation.title;
    }
  }
};

const handleDeleteConversation = async (conversationId: string) => {
  closeContextMenu();

  try {
    await ElMessageBox.confirm(
      'This action will permanently delete the conversation and all its messages. This cannot be undone.',
      'Delete Conversation',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    await deleteConversation(conversationId);
    ElMessage.success('Conversation deleted successfully');

    // If the deleted conversation was the current one, navigate to home
    if (router.currentRoute.value.params.id === conversationId) {
      router.push('/chat');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || 'Failed to delete conversation');
    }
  }
};

// Debounced search function
const debouncedSearch = debounce(async (query: string) => {
  if (query.trim()) {
    try {
      searchResults.value = await searchConversations(query);
    } catch (error) {
      console.error('Search failed:', error);
      searchResults.value = [];
    }
  } else {
    searchResults.value = [];
  }
}, 300);

// Watch search query changes
watch(searchQuery, (newQuery) => {
  debouncedSearch(newQuery);
});

// Close context menu when clicking outside
const handleClickOutside = (event: Event) => {
  if (contextMenuVisible.value) {
    const target = event.target as Element;
    if (!target.closest('.conversation-context-menu') && !target.closest('.context-menu-trigger')) {
      closeContextMenu();
    }
  }
};

onMounted(() => {
  loadConversations();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.search-input :deep(.el-input__wrapper) {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  background: hsl(var(--background));
  box-shadow: none;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: hsl(var(--ring));
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

.mode-tabs {
  background-color: hsl(var(--border));
  padding-top: 2px;
  padding-bottom: 2px;
  border: 3px solid hsl(var(--border));
  border-radius: 10px;
}

.mode-tabs :deep(.el-segmented__item) {
  color: hsl(var(--foreground));
  padding-top: 5px;
  padding-bottom: 5px;
}

.mode-tabs :deep(.el-segmented__item-selected) {
  background: hsl(var(--background));
  border-radius: 8px;
}

.mode-tabs :deep(.el-segmented__item.is-selected) {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

/* Context menu styles */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: transparent;
}

.context-menu {
  position: fixed;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  min-width: 160px;
  z-index: 10000;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  color: hsl(var(--foreground));
}

.context-menu-item:hover {
  background: hsl(var(--muted) / 0.5);
}

.context-menu-item--danger {
  color: hsl(var(--destructive));
}

.context-menu-item--danger:hover {
  background: hsl(var(--destructive) / 0.1);
}

.context-menu-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

/* Inline rename input styles */
.rename-input {
  width: 100%;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--primary));
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
  transition: all 0.2s ease;
}

.rename-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.3);
}

.rename-input::selection {
  background: hsl(var(--primary) / 0.3);
}
</style>
