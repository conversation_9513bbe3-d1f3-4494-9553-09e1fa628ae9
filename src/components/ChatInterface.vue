<template>
  <div class="flex-1 flex h-full">
    <!-- Main Chat Area with Drag and Drop -->
    <div
      class="flex-1 flex flex-col h-full relative"
      @dragenter="handleDragEnter"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
    >
      <!-- Full-Screen Drag and Drop Overlay -->
      <div
        v-if="isDragging"
        class="absolute inset-0 bg-primary/10 backdrop-blur-sm border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-50"
      >
        <div class="text-center p-8">
          <div class="w-24 h-24 mx-auto bg-primary/20 rounded-2xl flex items-center justify-center mb-6">
            <i class="ri-upload-cloud-2-line text-4xl text-primary"></i>
          </div>
          <h3 class="text-xl font-semibold text-primary mb-2">Drop files here to upload</h3>
          <p class="text-muted-foreground mb-4">
            Upload documents to enhance your conversation
          </p>
          <div class="text-sm text-muted-foreground">
            Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF
          </div>
        </div>
      </div>
      <!-- Header -->
      <ChatHeader 
        :title="headerTitle"
        :document-count="documentCount"
        :message-count="messageCount"
        @previous-page="handlePreviousPage"
        @next-page="handleNextPage"
        @close="handleClose"
        @export="handleExport"
        @share="handleShare"
      />

      <!-- Messages Area -->
      <ScrollArea ref="scrollAreaRef" class="flex-1 px-6 py-4">
        <div class="max-w-4xl mx-auto">
          <!-- Welcome Message -->
          <div
            v-if="displayedMessages.length === 0 && !isLoading"
            class="text-center py-12"
          >
            <div class="mb-4">
              <div class="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <MessageSquare class="h-8 w-8 text-primary" />
              </div>
            </div>
            <h3 class="text-lg font-semibold mb-2">Start a conversation</h3>
            <p class="text-muted-foreground mb-4">
              Ask questions about your documents or start a new topic.
            </p>
            <div class="flex flex-wrap gap-2 justify-center">
              <Button
                v-for="suggestion in suggestions"
                :key="suggestion"
                variant="outline"
                size="sm"
                @click="handleSuggestionClick(suggestion)"
              >
                {{ suggestion }}
              </Button>
            </div>
          </div>

          <!-- Messages -->
          <div v-else>
            <ChatMessage
              v-for="message in displayedMessages"
              :key="message.id"
              :message="message"
              @document-click="handleDocumentClick"
            />

            <!-- Loading Indicator -->
            <div
              v-if="isLoading"
              class="flex gap-3 mb-4"
            >
              <Avatar size="sm" class="bg-muted text-muted-foreground">
                <template #fallback>
                  <Bot class="h-3 w-3" />
                </template>
              </Avatar>
              <div class="flex-1">
                <div class="mb-1">
                  <span class="text-xs text-muted-foreground">AI Assistant</span>
                </div>
                <div class="inline-block px-3 py-2 rounded-lg bg-background border border-border shadow-sm">
                  <div class="flex items-center gap-2">
                    <Loader2 class="h-4 w-4 animate-spin" />
                    <span class="text-sm text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div
            v-if="error"
            class="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg"
          >
            <div class="flex items-center gap-2">
              <AlertCircle class="h-4 w-4 text-destructive" />
              <span class="text-sm text-destructive">{{ error }}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              class="mt-2"
              @click="clearError"
            >
              Dismiss
            </Button>
          </div>
        </div>
      </ScrollArea>

      <!-- Input -->
      <ChatInput
        ref="chatInputRef"
        :disabled="isLoading"
        :disable-drag-overlay="true"
        @send-message="handleSendMessage"
        @file-upload="handleFileUpload"
      />
    </div>

    <!-- Document Preview Panel -->
    <DocumentPreview
      v-if="selectedDocument"
      :document-name="selectedDocument"
      @close="handleCloseDocument"
      @download="handleDownloadDocument"
      @open-external="handleOpenExternal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { MessageSquare, Bot, Loader2, AlertCircle } from 'lucide-vue-next';
import type { ChatInterfaceProps } from '@/types';
import { useChat } from '@/composables/useChat';
import { useUI } from '@/composables/useUI';
import { useDocuments } from '@/composables/useDocuments';
import ChatHeader from '@/components/ChatHeader.vue';
import ChatMessage from '@/components/ChatMessage.vue';
import ChatInput from '@/components/ChatInput.vue';
import DocumentPreview from '@/components/DocumentPreview.vue';
import ScrollArea from '@/components/ui/ScrollArea.vue';
import Button from '@/components/ui/Button.vue';
import Avatar from '@/components/ui/Avatar.vue';

const props = defineProps<ChatInterfaceProps>();

const emit = defineEmits<{
  conversationChange: [id: string];
}>();

const {
  selectedConversation,
  currentMessages,
  sendMessage,
  isLoading,
  error,
  clearError,
  clearLoadingState
} = useChat();

// Local state for managing displayed messages
const displayedMessages = ref([]);

const { selectedDocument, openDocumentPreview, closeDocumentPreview } = useUI();
const { uploadDocuments } = useDocuments();

const scrollAreaRef = ref();
const chatInputRef = ref();

// Drag and drop state
const isDragging = ref(false);
const dragCounter = ref(0); // Track drag enter/leave events

// Computed properties
const headerTitle = computed(() => 
  selectedConversation.value?.title || 'New Chat'
);

const documentCount = computed(() => 
  selectedConversation.value?.documentCount || 0
);

const messageCount = computed(() =>
  displayedMessages.value.length
);

// Sample suggestions for empty state
const suggestions = [
  'Explain work-energy theorem',
  'Help with physics homework',
  'Summarize my notes',
  'Create practice problems'
];

// Methods
const handleSendMessage = async (content: string, files?: File[]) => {
  try {
    console.log('ChatInterface: Sending message with conversationId:', props.conversationId);

    // Upload files if provided
    if (files && files.length > 0 && props.conversationId) {
      await uploadDocuments(props.conversationId, files);
    }

    // Send message with the conversation ID from props (can be null for new conversations)
    await sendMessage(content, props.conversationId || null);

    // Scroll to bottom after message is sent
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error('Failed to send message:', error);
  }
};

const handleFileUpload = async (files: File[]) => {
  // For new conversations (no ID), files will be handled when the first message is sent
  if (!props.conversationId) {
    console.log('Files uploaded for new conversation, will be processed with first message');
    return;
  }

  try {
    await uploadDocuments(props.conversationId, files);
  } catch (error) {
    console.error('Failed to upload files:', error);
  }
};

const handleDocumentClick = (documentName: string) => {
  openDocumentPreview(documentName);
};

const handleCloseDocument = () => {
  closeDocumentPreview();
};

const handleDownloadDocument = (documentName: string) => {
  // TODO: Implement document download
  console.log('Download document:', documentName);
};

const handleOpenExternal = (documentName: string) => {
  // TODO: Implement open in external viewer
  console.log('Open external:', documentName);
};

// Drag and drop handlers
const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();

  // Only track drag enter for files
  if (event.dataTransfer?.types.includes('Files')) {
    dragCounter.value++;
    isDragging.value = true;
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'copy';
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();

  // Use drag counter to properly handle nested elements
  dragCounter.value--;

  if (dragCounter.value <= 0) {
    isDragging.value = false;
    dragCounter.value = 0;
  }
};

const handleDrop = async (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  dragCounter.value = 0;

  const files = Array.from(event.dataTransfer?.files || []);
  if (files.length > 0) {
    console.log('ChatInterface: Files dropped:', files.map(f => f.name));

    // Validate file types (same as ChatInput)
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ];

    const validFiles = files.filter(file =>
      allowedTypes.includes(file.type) ||
      /\.(pdf|doc|docx|txt|jpg|jpeg|png|gif)$/i.test(file.name)
    );

    if (validFiles.length !== files.length) {
      console.warn('Some files were filtered out due to unsupported types');
    }

    if (validFiles.length > 0) {
      // Add files to ChatInput component for display in file preview
      if (chatInputRef.value?.addFiles) {
        chatInputRef.value.addFiles(validFiles);
      }
    }
  }
};

const handleSuggestionClick = (suggestion: string) => {
  handleSendMessage(suggestion);
};

const handlePreviousPage = () => {
  // TODO: Implement pagination
  console.log('Previous page');
};

const handleNextPage = () => {
  // TODO: Implement pagination
  console.log('Next page');
};

const handleClose = () => {
  // TODO: Implement close chat
  console.log('Close chat');
};

const handleExport = () => {
  // TODO: Implement export chat
  console.log('Export chat');
};

const handleShare = () => {
  // TODO: Implement share chat
  console.log('Share chat');
};

const scrollToBottom = () => {
  if (scrollAreaRef.value) {
    scrollAreaRef.value.scrollToBottom();
  }
};

// Watch for new messages and scroll to bottom
watch(displayedMessages, () => {
  nextTick(() => {
    scrollToBottom();
  });
}, { deep: true });

// Sync displayedMessages with currentMessages when conversation exists
watch(currentMessages, (newMessages) => {
  if (props.conversationId) {
    displayedMessages.value = [...newMessages];
  }
}, { deep: true, immediate: true });

// Watch for conversation changes and clear messages for new conversations
watch(() => props.conversationId, (newId, oldId) => {
  console.log('ChatInterface: Conversation ID changed from', oldId, 'to', newId);
  console.log('ChatInterface: Current loading state:', isLoading.value);

  if (newId === null || newId === undefined) {
    // Clear messages for new conversation
    console.log('ChatInterface: Clearing messages for new conversation');
    displayedMessages.value = [];

    // Clear any loading state for new conversations
    if (isLoading.value) {
      console.log('ChatInterface: Clearing loading state for new conversation');
      clearLoadingState();
    }
  } else {
    // Load messages for existing conversation
    console.log('ChatInterface: Loading messages for conversation', newId);
    displayedMessages.value = [...currentMessages.value];
    emit('conversationChange', newId);
  }
}, { immediate: true });

onMounted(() => {
  // Scroll to bottom on mount
  nextTick(() => {
    scrollToBottom();
  });
});
</script>
