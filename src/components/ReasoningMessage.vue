<template>
  <div class="reasoning-wrapper">
    <div class="reasoning-title" @click="toggle">
      <div>
        <div v-if="loading" class="loader-wrapper">
          <span class="glow-text">Is Thinking...</span>
        </div>
        <span v-if="!loading">Thought for {{ timeRange }} seconds</span>
      </div>
      <ChevronDown v-if="!isDrop" class="h-4 w-4 text-muted-foreground" />
      <ChevronUp v-if="isDrop" class="h-4 w-4 text-muted-foreground" />
    </div>
    <div ref="messageRef" :class="{'reasoning-messages': true}" style="height: 0;">
      <div class="markdown-content" v-html="renderedContent"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ChevronDown, ChevronUp } from 'lucide-vue-next';
import { renderMarkdown } from '@/utils/markdown';

interface ReasoningMessageProps {
  id: string;
  reasoningContent: string;
  loading: boolean;
  timeRange: number;
}

const messageRef = ref<HTMLElement>();
const props = defineProps<ReasoningMessageProps>();
const isDrop = ref(false);

const renderedContent = computed(() => {
  return renderMarkdown(props.reasoningContent);
});

function open() {
  isDrop.value = true;
  if (messageRef.value) {
    messageRef.value.style.height = messageRef.value.scrollHeight + 'px';

    const onEnd = (e: TransitionEvent) => {
      if (e.propertyName !== 'height') return;
      if (messageRef.value) {
        messageRef.value.style.height = 'auto';
        messageRef.value.removeEventListener('transitionend', onEnd);
      }
    };
    messageRef.value.addEventListener('transitionend', onEnd);
  }
}

function close() {
  if (messageRef.value) {
    messageRef.value.style.height = messageRef.value.offsetHeight + 'px';
    messageRef.value.offsetHeight; // Force reflow
    messageRef.value.style.height = '0px';
  }
}

function toggle() {
  isDrop.value = false;
  const isClosed = !messageRef.value || 
    messageRef.value.style.height === '' || 
    messageRef.value.style.height === '0px';
  
  if (isClosed) {
    open();
  } else {
    close();
  }
}
</script>

<style scoped>
.reasoning-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 16px 0;
}

.reasoning-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: hsl(var(--muted-foreground));
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.reasoning-title:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.reasoning-messages {
  padding: 0 0 0 20px;
  box-sizing: border-box;
  border-left: 2px solid hsl(var(--border));
  transform-origin: top;
  transition: all 0.35s ease;
  overflow: hidden;
}

.reasoning-messages :deep(p) {
  margin: 20px 0;
}

.glow-text {
  position: relative;
  font-weight: bold;
  font-size: 14px;
  background: linear-gradient(
      120deg,
      hsl(var(--foreground)),
      hsl(var(--muted-foreground)),
      hsl(var(--foreground))
  );
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: shimmer 2s infinite linear;
}

@keyframes shimmer {
  0% {
    background-position: 200% center;
  }
  100% {
    background-position: -200% center;
  }
}

.loader-wrapper {
  display: flex;
  gap: 5px;
  align-items: center;
  width: fit-content;
}
</style>
