// Core data types for the Vue 3 chat application

export interface Document {
  name: string;
  pages?: string;
  content?: string;
  type?: 'pdf' | 'doc' | 'txt' | 'image';
  size?: number;
  uploadedAt?: string;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  documents?: Document[];
  isLoading?: boolean;
  error?: string;
  reasoning?: ReasoningData;
}

export interface ReasoningData {
  id: string;
  content: string;
  loading: boolean;
  timeRange: number;
}

export interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  documentCount?: number;
  messages?: Message[];
  isActive?: boolean;
}

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: 'user' | 'admin';
}

export interface ChatState {
  conversations: Conversation[];
  selectedConversationId: string | null;
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  mode: 'conversation' | 'document';
}

export interface UIState {
  isSidebarCollapsed: boolean;
  selectedDocument: string | null;
  theme: 'light' | 'dark';
  isDocumentPreviewOpen: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// API request/response types
export interface SendMessageRequest {
  conversationId: string;
  content: string;
  documents?: File[];
}

export interface SendMessageResponse {
  message: Message;
  aiResponse?: Message;
  streamResponse?: any; // For handling streaming responses from the chat API
}

export interface CreateConversationRequest {
  title?: string;
  initialMessage?: string;
  collection_name?: string;
}

export interface CreateConversationResponse {
  conversation: Conversation;
}

export interface UploadDocumentRequest {
  conversationId: string;
  files: File[];
}

export interface UploadDocumentResponse {
  documents: Document[];
}

// Component prop types
export interface ChatMessageProps {
  message: Message;
  onDocumentClick?: (documentName: string) => void;
}

export interface ChatInputProps {
  onSendMessage?: (content: string, files?: File[]) => void;
  onFileUpload?: (files: File[]) => void;
  disabled?: boolean;
  placeholder?: string;
  disableDragOverlay?: boolean; // Disable drag overlay when parent handles it
}

export interface ChatSidebarProps {
  selectedConversation?: string;
  onSelectConversation?: (id: string) => void;
  onNewConversation?: () => void;
  mode?: 'conversation' | 'document';
  onModeChange?: (mode: 'conversation' | 'document') => void;
  isSidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

export interface ChatInterfaceProps {
  conversationId?: string;
}

export interface DocumentPreviewProps {
  documentName: string;
  onClose: () => void;
}

export interface ReasoningMessageProps {
  id: string;
  reasoningContent: string;
  loading: boolean;
  timeRange: number;
}

// Utility types
export type MessageSender = 'user' | 'assistant';
export type AppMode = 'conversation' | 'document';
export type Theme = 'light' | 'dark';
export type DocumentType = 'pdf' | 'doc' | 'txt' | 'image';

// Event types
export interface ChatEvents {
  'message:send': SendMessageRequest;
  'message:receive': Message;
  'conversation:select': string;
  'conversation:create': CreateConversationRequest;
  'document:upload': UploadDocumentRequest;
  'document:preview': string;
  'sidebar:toggle': boolean;
  'theme:change': Theme;
}
