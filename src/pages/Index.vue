<template>
  <div class="h-screen bg-background flex flex-col overflow-hidden">
    <div class="flex-1 flex overflow-hidden">
      <!-- Sidebar -->
      <Transition
        name="sidebar"
        enter-active-class="transition-transform duration-300 ease-out"
        leave-active-class="transition-transform duration-300 ease-in"
        enter-from-class="-translate-x-full"
        enter-to-class="translate-x-0"
        leave-from-class="translate-x-0"
        leave-to-class="-translate-x-full"
      >
        <ChatSidebar 
          v-if="!isSidebarCollapsed"
          :is-sidebar-collapsed="isSidebarCollapsed"
          :selected-conversation="selectedConversationId"
          :mode="mode"
          @select-conversation="handleSelectConversation"
          @new-conversation="handleNewConversation"
          @mode-change="handleModeChange"
          @toggle-sidebar="handleToggleSidebar"
        />
      </Transition>
      
      <!-- Main Chat Area -->
      <ChatInterface
        :conversation-id="selectedConversationId"
        @conversation-change="handleConversationChange"
      />
    </div>

    <!-- Loading Overlay -->
    <Transition
      name="fade"
      enter-active-class="transition-opacity duration-200"
      leave-active-class="transition-opacity duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isInitializing"
        class="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div class="text-center">
          <Loader2 class="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p class="text-sm text-muted-foreground">Loading conversations...</p>
        </div>
      </div>
    </Transition>

    <!-- Error Toast -->
    <Transition
      name="slide-up"
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="translate-y-full"
      enter-to-class="translate-y-0"
      leave-from-class="translate-y-0"
      leave-to-class="translate-y-full"
    >
      <div
        v-if="globalError"
        class="fixed bottom-4 right-4 bg-destructive text-destructive-foreground p-4 rounded-lg shadow-lg max-w-md z-40"
      >
        <div class="flex items-center gap-2">
          <AlertCircle class="h-4 w-4 flex-shrink-0" />
          <span class="text-sm">{{ globalError }}</span>
          <Button
            variant="ghost"
            size="sm"
            class="h-6 w-6 p-0 ml-auto hover:bg-destructive-foreground/20"
            @click="clearGlobalError"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { Loader2, AlertCircle, X } from 'lucide-vue-next';
import { useChat } from '@/composables/useChat';
import { useUI } from '@/composables/useUI';
import ChatSidebar from '@/components/ChatSidebar.vue';
import ChatInterface from '@/components/ChatInterface.vue';
import Button from '@/components/ui/Button.vue';

// Props (from router)
interface Props {
  id?: string;
}

const props = withDefaults(defineProps<Props>(), {
  id: undefined
});

// Router
const router = useRouter();

// Composables
const {
  selectedConversation,
  selectConversation,
  setMode,
  initialize,
  setSelectedConversationId,
  validateConversationExists,
  areConversationsLoaded,
  isLoading,
  error: chatError,
  clearError: clearChatError
} = useChat();

const { 
  isSidebarCollapsed, 
  toggleSidebar,
  cleanup: cleanupUI 
} = useUI();

// Local state
const selectedConversationId = ref<string | null>(props.id || null);
const mode = ref<'conversation' | 'document'>('conversation');
const isInitializing = ref(true);
const globalError = ref<string | null>(null);

// Computed
const hasSelectedConversation = computed(() => !!selectedConversationId.value);

// Watch for route changes with conversation validation
watch(() => props.id, async (newId) => {
  console.log('Index: Route ID changed to:', newId);

  if (!newId) {
    // No ID means new conversation - show clean slate
    selectedConversationId.value = null;
    setSelectedConversationId(null);
    return;
  }

  // Wait for conversations to be loaded before validation
  if (!areConversationsLoaded()) {
    console.log('Index: Waiting for conversations to load before validation');
    return;
  }

  // Validate conversation exists
  if (!validateConversationExists(newId)) {
    console.log('Index: Conversation not found, redirecting to NotFound:', newId);
    router.replace({ name: 'NotFound' });
    return;
  }

  // Valid conversation ID - set it
  selectedConversationId.value = newId;
  setSelectedConversationId(newId);
}, { immediate: true });

// Watch for conversations loading completion to validate current route
watch(() => areConversationsLoaded(), (loaded) => {
  if (loaded && props.id) {
    // Re-validate current route after conversations are loaded
    if (!validateConversationExists(props.id)) {
      console.log('Index: Conversation not found after loading, redirecting to NotFound:', props.id);
      router.replace({ name: 'NotFound' });
    } else {
      // Valid conversation - ensure it's selected
      selectedConversationId.value = props.id;
      setSelectedConversationId(props.id);
    }
  }
});

// Methods
const handleSelectConversation = async (id: string) => {
  // Prevent selecting the same conversation or during loading
  if (selectedConversationId.value === id || isLoading.value) {
    console.log('Index: Skipping conversation selection - same ID or loading');
    return;
  }

  try {
    console.log('Index: Selecting conversation:', id);
    await selectConversation(id);
    selectedConversationId.value = id;
  } catch (error: any) {
    console.error('Index: Failed to select conversation:', error);
    globalError.value = error.message || 'Failed to select conversation';
  }
};

const handleNewConversation = async () => {
  console.log('Index: Starting new conversation (no ID)');

  // Set conversation ID to null for new conversation
  selectedConversationId.value = null;

  // Navigation is already handled by ChatSidebar component
  // This provides a clean slate for new conversations
};

const handleModeChange = (newMode: 'conversation' | 'document') => {
  mode.value = newMode;
  setMode(newMode);
};

const handleToggleSidebar = () => {
  toggleSidebar();
};

const handleConversationChange = (id: string) => {
  selectedConversationId.value = id;
};

const handleHome = () => {
  // TODO: Implement home navigation
  console.log('Navigate to home');
};

const handleSearch = () => {
  // TODO: Implement global search
  console.log('Open global search');
};

const handleSettings = () => {
  // TODO: Implement settings
  console.log('Open settings');
};

const handleHelp = () => {
  // TODO: Implement help
  console.log('Open help');
};

const clearGlobalError = () => {
  globalError.value = null;
  clearChatError();
};

// Auto-clear errors after 5 seconds
const autoHideError = () => {
  if (globalError.value) {
    setTimeout(() => {
      globalError.value = null;
    }, 5000);
  }
};

// Watch for chat errors
const unwatchChatError = computed(() => {
  if (chatError.value && !globalError.value) {
    globalError.value = chatError.value;
    autoHideError();
  }
  return chatError.value;
});

// Watch for changes in selected conversation to keep state in sync
watch(selectedConversation, (newConversation) => {
  if (newConversation && selectedConversationId.value !== newConversation.id) {
    console.log('Index: Syncing selectedConversationId with global state:', newConversation.id);
    selectedConversationId.value = newConversation.id;
  }
});

// Initialize the application
onMounted(async () => {
  try {
    await initialize();

    // The route watcher will handle setting the correct conversation ID
    // Don't automatically select any conversation here
    console.log('Index: Application initialized, selectedConversationId:', selectedConversationId.value);
  } catch (error: any) {
    globalError.value = error.message || 'Failed to initialize application';
  } finally {
    isInitializing.value = false;
  }
});

// Cleanup on unmount
onUnmounted(() => {
  cleanupUI();
});
</script>

<style scoped>
/* Transition styles */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform 0.3s ease;
}

.sidebar-enter-from {
  transform: translateX(-100%);
}

.sidebar-leave-to {
  transform: translateX(-100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
