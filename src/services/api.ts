import { useAxios } from '@/lib/axios'
import type { Message } from '@/types'


export async function chat(sessionId: string, messages: Message[], collection_name: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/assistant/chat-with-docs`
  const http = useAxios()

  const data = {
    "session_id": sessionId,
    "messages": messages,
    "collection_name": collection_name
  }

  return http.post(url, JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json'
    },
    adapter: "fetch",
    responseType: "stream",
  })
}

export async function getAllSessions() {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.get(url)
}

export async function getSessionHistory(sessionId: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-messages/${sessionId}`
  const http = useAxios()
  return http.get(url)
}

export async function createSession() {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session`
  const http = useAxios()
  return http.post(url)
}

export async function updateSessionName(sessionId: string, updated_name: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}?updated_name=${updated_name}`
  const http = useAxios()
  return http.post(url)
}

export async function deleteSession(sessionId: string) {
  const url = `/${import.meta.env.VITE_API_VERSION}/chat-session/${sessionId}`
  const http = useAxios()
  return http.delete(url)
}
